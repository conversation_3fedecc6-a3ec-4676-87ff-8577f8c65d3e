// Integration tests for Raydium transaction bundling with Jito
const { buyRaydium } = require('../utils/raydiumBuyOperations');
const { sellRaydium } = require('../utils/raydiumSellOperations');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');
jest.mock('../utils/config');

const raydiumConfig = require('../utils/raydiumConfig');
const transactionUtils = require('../utils/transactionUtils');
const config = require('../utils/config');

describe('Transaction Bundling Integration Tests', () => {
  const mockWallets = [
    { encryptedPrivateKey: 'encrypted_key_1' },
    { encryptedPrivateKey: 'encrypted_key_2' },
    { encryptedPrivateKey: 'encrypted_key_3' }
  ];

  const mockAmounts = {
    'MockPublicKey': '0.1'
  };

  const mockToken = 'TokenMintAddress123';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Raydium SDK
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    
    // Mock logging functions
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockReturnValue({
      category: 'UNKNOWN_ERROR',
      severity: 'HIGH',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    });

    // Mock config
    config.getTokenDecimals.mockResolvedValue(9);
    config.connection = global.testUtils.generateMockConnection();

    // Mock transaction utilities
    transactionUtils.getLatestBlockhashWithRetry.mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    });
    transactionUtils.submitTransactionToJito.mockResolvedValue('mock_jito_signature');
    transactionUtils.submitBundle.mockResolvedValue({
      success: true,
      bundleId: 'mock_bundle_id',
      signatures: ['sig1', 'sig2', 'sig3']
    });
  });

  describe('Jito bundling integration', () => {
    test('should handle Jito tip transactions for buy operations', async () => {
      const jitoTip = '0.001'; // Non-zero tip should trigger Jito
      
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, jitoTip, false);

      // Should process all wallets (even though they fail due to placeholder)
      expect(result.totalWallets).toBe(3);
      expect(result.platform).toBe('raydium');
      
      // Verify Jito tip logging was called
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Adding Jito tip of 0.001 SOL')
      );
    });

    test('should handle Jito tip transactions for sell operations', async () => {
      const jitoTip = '0.0005';
      
      const result = await sellRaydium(mockWallets, 75, mockToken, jitoTip, false);

      expect(result.totalWallets).toBe(3);
      expect(result.platform).toBe('raydium');
      
      // Verify Jito tip logging was called
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Adding Jito tip of 0.0005 SOL')
      );
    });

    test('should handle zero Jito tip (regular RPC)', async () => {
      const jitoTip = '0'; // Zero tip should use regular RPC
      
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, jitoTip, false);

      expect(result.totalWallets).toBe(3);
      
      // Should not log Jito tip messages
      expect(raydiumConfig.logWithTimestamp).not.toHaveBeenCalledWith(
        expect.stringContaining('Adding Jito tip')
      );
    });

    test('should handle Jito submission failures gracefully', async () => {
      transactionUtils.submitTransactionToJito.mockRejectedValue(
        new Error('Jito submission failed')
      );
      
      const jitoTip = '0.001';
      
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, jitoTip, false);

      // Should still process wallets and handle failures
      expect(result.totalWallets).toBe(3);
      expect(result.failedTxs).toBe(3);
    });
  });

  describe('Bundle processing validation', () => {
    test('should respect bundle size limits', async () => {
      // Test with number of wallets equal to bundle limit
      const bundleLimitWallets = Array(3).fill().map((_, i) => ({
        encryptedPrivateKey: `encrypted_key_${i}`
      }));

      const bundleLimitAmounts = {};
      bundleLimitWallets.forEach((_, i) => {
        bundleLimitAmounts['MockPublicKey'] = '0.1';
      });

      const result = await buyRaydium(bundleLimitWallets, bundleLimitAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      // Should process all wallets in the bundle
    });

    test('should handle bundle size exceeding limits', async () => {
      // Test with more wallets than bundle limit
      const manyWallets = Array(10).fill().map((_, i) => ({
        encryptedPrivateKey: `encrypted_key_${i}`
      }));

      const manyAmounts = {};
      manyWallets.forEach((_, i) => {
        manyAmounts['MockPublicKey'] = '0.1';
      });

      const result = await buyRaydium(manyWallets, manyAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(10);
      // Should process all wallets even if they exceed bundle limits
    });

    test('should handle bundle submission timeouts', async () => {
      transactionUtils.submitBundle.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Bundle timeout')), 100)
        )
      );

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      // Should handle timeout gracefully
    });
  });

  describe('Transaction status tracking', () => {
    test('should track transaction signatures correctly', async () => {
      // Mock successful transaction submission
      transactionUtils.submitTransactionToJito.mockResolvedValue('successful_signature');
      
      const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, '0.001', false);

      // Even though the operation fails due to placeholder, signature tracking should work
      expect(result.totalWallets).toBe(1);
      expect(result.platform).toBe('raydium');
    });

    test('should handle signature verification failures', async () => {
      transactionUtils.submitTransactionToJito.mockResolvedValue(null);
      
      const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(1);
      expect(result.failedTxs).toBe(1);
    });

    test('should track bundle status across multiple transactions', async () => {
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      expect(result.duration).toBeGreaterThan(0);
      
      // Should track all wallet operations
      expect(result.errors).toHaveLength(3); // All fail due to placeholder
    });
  });

  describe('Performance under bundling', () => {
    test('should complete bundled operations within time limits', async () => {
      const startTime = Date.now();
      
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);
      
      const endTime = Date.now();
      const operationTime = endTime - startTime;

      expect(operationTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(result.duration).toBeGreaterThan(0);
    });

    test('should handle concurrent bundle operations', async () => {
      const buyPromise = buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);
      const sellPromise = sellRaydium(mockWallets, 50, mockToken, '0.001', false);

      const [buyResult, sellResult] = await Promise.all([buyPromise, sellPromise]);

      expect(buyResult.platform).toBe('raydium');
      expect(sellResult.platform).toBe('raydium');
      expect(buyResult.totalWallets).toBe(3);
      expect(sellResult.totalWallets).toBe(3);
    });
  });

  describe('Error recovery in bundling', () => {
    test('should retry failed bundle submissions', async () => {
      let callCount = 0;
      transactionUtils.submitTransactionToJito.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw new Error('First attempt failed');
        }
        return Promise.resolve('retry_success_signature');
      });

      const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(1);
      // Should handle retry logic (though it still fails due to placeholder)
    });

    test('should handle partial bundle failures', async () => {
      // Mock some transactions succeeding and others failing
      transactionUtils.submitTransactionToJito
        .mockResolvedValueOnce('success_signature_1')
        .mockRejectedValueOnce(new Error('Transaction 2 failed'))
        .mockResolvedValueOnce('success_signature_3');

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      // Should handle mixed success/failure scenarios
    });

    test('should maintain bundle integrity during failures', async () => {
      transactionUtils.getLatestBlockhashWithRetry.mockRejectedValue(
        new Error('Blockhash retrieval failed')
      );

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      expect(result.failedTxs).toBe(3);
      // Should fail gracefully without corrupting state
    });
  });

  describe('Bundle configuration validation', () => {
    test('should use correct priority fees for bundling', async () => {
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      
      // Should use Raydium-specific priority fee configuration
      expect(raydiumConfig.getRaydiumSdkWithInit).toHaveBeenCalled();
    });

    test('should handle different tip amounts correctly', async () => {
      const tipAmounts = ['0', '0.0001', '0.001', '0.01'];
      
      for (const tip of tipAmounts) {
        const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, tip, false);
        expect(result.platform).toBe('raydium');
        expect(result.totalWallets).toBe(1);
      }
    });

    test('should validate bundle transaction limits', async () => {
      // Test with exactly the maximum allowed transactions
      const maxWallets = Array(raydiumConfig.RAYDIUM_CONFIG?.MAX_TRANSACTIONS_PER_BUNDLE || 3)
        .fill().map((_, i) => ({ encryptedPrivateKey: `key_${i}` }));

      const maxAmounts = {};
      maxWallets.forEach((_, i) => {
        maxAmounts['MockPublicKey'] = '0.1';
      });

      const result = await buyRaydium(maxWallets, maxAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(maxWallets.length);
    });
  });

  describe('Integration with existing bundling infrastructure', () => {
    test('should work with existing Jito tip accounts', async () => {
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.totalWallets).toBe(3);
      // Should use shared Jito tip accounts from config
    });

    test('should maintain compatibility with PumpFun bundling', async () => {
      // Test that Raydium bundling doesn't interfere with existing patterns
      const raydiumResult = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);
      
      expect(raydiumResult.platform).toBe('raydium');
      expect(raydiumResult.totalWallets).toBe(3);
      
      // Should maintain same result structure as PumpFun operations
      expect(raydiumResult).toHaveProperty('success');
      expect(raydiumResult).toHaveProperty('totalWallets');
      expect(raydiumResult).toHaveProperty('successfulTxs');
      expect(raydiumResult).toHaveProperty('failedTxs');
      expect(raydiumResult).toHaveProperty('results');
      expect(raydiumResult).toHaveProperty('errors');
      expect(raydiumResult).toHaveProperty('duration');
      expect(raydiumResult).toHaveProperty('platform');
    });
  });
});
