# Troubleshooting Guide and FAQ

## Overview

This guide provides solutions to common issues encountered when working with the Raydium SDK V2 launchpad integration, along with frequently asked questions and debugging techniques.

## Table of Contents

1. [Common Issues](#common-issues)
2. [Error Messages](#error-messages)
3. [Performance Issues](#performance-issues)
4. [Configuration Problems](#configuration-problems)
5. [Network and Connectivity](#network-and-connectivity)
6. [Debugging Techniques](#debugging-techniques)
7. [Frequently Asked Questions](#frequently-asked-questions)
8. [Getting Help](#getting-help)

## Common Issues

### 1. SDK Initialization Failures

**Symptoms:**
- "Raydium SDK not initialized" errors
- Application fails to start
- Timeout errors during initialization

**Causes & Solutions:**

```bash
# Check RPC connectivity
curl -X POST $SOLANA_RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}'

# Verify environment variables
echo $SOLANA_NETWORK
echo $SOLANA_RPC_URL
echo $RAYDIUM_PROGRAM_ID

# Test with different RPC endpoint
export SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
npm restart
```

**Prevention:**
- Use multiple RPC endpoints for redundancy
- Implement proper retry logic
- Monitor RPC endpoint health

### 2. Transaction Failures

**Symptoms:**
- "Transaction simulation failed" errors
- High failure rates in buy/sell operations
- Slippage errors

**Diagnostic Steps:**

```bash
# Check wallet balances
node -e "
const { Connection, PublicKey } = require('@solana/web3.js');
const connection = new Connection(process.env.SOLANA_RPC_URL);
connection.getBalance(new PublicKey('YOUR_WALLET_ADDRESS'))
  .then(balance => console.log('Balance:', balance / 1e9, 'SOL'));
"

# Verify token account existence
node -e "
const { Connection, PublicKey } = require('@solana/web3.js');
const connection = new Connection(process.env.SOLANA_RPC_URL);
connection.getTokenAccountsByOwner(
  new PublicKey('YOUR_WALLET_ADDRESS'),
  { mint: new PublicKey('TOKEN_MINT_ADDRESS') }
).then(accounts => console.log('Token accounts:', accounts.value.length));
"
```

**Solutions:**
- Increase slippage tolerance
- Check wallet SOL balance for fees
- Verify token mint address validity
- Use smaller transaction amounts

### 3. Memory Leaks and Performance Degradation

**Symptoms:**
- Increasing memory usage over time
- Slower response times
- Application crashes with out-of-memory errors

**Monitoring:**

```bash
# Monitor memory usage
pm2 monit

# Check for memory leaks
node --inspect server.js
# Then use Chrome DevTools to analyze heap

# Monitor with htop
htop -p $(pgrep -f "node.*server.js")
```

**Solutions:**

```javascript
// Add to your application
process.on('warning', (warning) => {
  console.warn('Warning:', warning.name, warning.message);
});

// Monitor memory usage
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB'
  });
}, 60000);
```

### 4. Database Connection Issues

**Symptoms:**
- "MongoNetworkError" messages
- Connection timeouts
- Authentication failures

**Diagnostic Commands:**

```bash
# Test MongoDB connection
mongo $MONGODB_URI --eval "db.runCommand({ping: 1})"

# Check connection string format
echo $MONGODB_URI | grep -E "mongodb://|mongodb\+srv://"

# Test with MongoDB Compass
mongosh $MONGODB_URI
```

**Solutions:**
- Verify connection string format
- Check network connectivity to MongoDB server
- Ensure proper authentication credentials
- Configure connection pooling appropriately

## Error Messages

### "launchpad functionality requires specific API implementation"

**Meaning:** This is expected behavior for the current placeholder implementation.

**Context:** The Raydium integration is currently implemented with placeholder functionality that validates inputs and provides proper error handling while the actual Raydium launchpad API methods are being researched and implemented.

**Action:** This is not an error - it's the expected response until the actual Raydium launchpad API is integrated.

### "Network connection issue. Please try again."

**Causes:**
- RPC endpoint is down or overloaded
- Network connectivity issues
- Firewall blocking connections

**Solutions:**

```bash
# Test network connectivity
ping api.mainnet-beta.solana.com

# Test RPC endpoint
curl -X POST $SOLANA_RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getVersion"}'

# Switch to backup RPC
export SOLANA_RPC_URL=$SOLANA_RPC_BACKUP_URL
```

### "Insufficient funds for this transaction"

**Causes:**
- Wallet SOL balance too low for transaction fees
- Requested buy amount exceeds available balance
- Gas estimation errors

**Solutions:**

```bash
# Check wallet balance
solana balance YOUR_WALLET_ADDRESS

# Check current gas prices
curl -X POST $SOLANA_RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getRecentPrioritizationFees","params":[["YOUR_PROGRAM_ID"]]}'
```

### "Price changed during transaction. Please try again."

**Causes:**
- High market volatility
- Slippage tolerance too low
- Delayed transaction execution

**Solutions:**
- Increase slippage tolerance
- Use smaller transaction amounts
- Retry with current market prices

## Performance Issues

### Slow Response Times

**Diagnostic Steps:**

```bash
# Profile API endpoints
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3001/api/health"

# Create curl-format.txt:
echo "
     time_namelookup:  %{time_namelookup}s
        time_connect:  %{time_connect}s
     time_appconnect:  %{time_appconnect}s
    time_pretransfer:  %{time_pretransfer}s
       time_redirect:  %{time_redirect}s
  time_starttransfer:  %{time_starttransfer}s
                     ----------
          time_total:  %{time_total}s
" > curl-format.txt
```

**Optimization:**

```javascript
// Add request timing middleware
app.use((req, res, next) => {
  req.startTime = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - req.startTime;
    if (duration > 5000) {
      console.warn(`Slow request: ${req.method} ${req.path} took ${duration}ms`);
    }
  });
  next();
});
```

### High CPU Usage

**Monitoring:**

```bash
# Monitor CPU usage by process
top -p $(pgrep -f "node.*server.js")

# Profile with Node.js
node --prof server.js
# Generate report after stopping
node --prof-process isolate-*.log > profile.txt
```

**Solutions:**
- Implement request queuing
- Optimize database queries
- Use clustering with PM2
- Cache frequently accessed data

## Configuration Problems

### Environment Variables Not Loading

**Check:**

```bash
# Verify .env file exists and is readable
ls -la .env
cat .env | head -5

# Test environment loading
node -e "require('dotenv').config(); console.log(process.env.NODE_ENV);"

# Check for syntax errors in .env
grep -n "=" .env | grep -v "^[A-Z_]*="
```

### Invalid Configuration Values

**Validation Script:**

```javascript
// validate-env.js
const required = ['MONGODB_URI', 'SOLANA_RPC_URL', 'JWT_SECRET'];
const missing = required.filter(key => !process.env[key]);

if (missing.length > 0) {
  console.error('Missing required environment variables:', missing);
  process.exit(1);
}

// Validate specific formats
if (!process.env.MONGODB_URI.startsWith('mongodb')) {
  console.error('Invalid MONGODB_URI format');
  process.exit(1);
}

if (process.env.ENCRYPTION_KEY?.length !== 32) {
  console.error('ENCRYPTION_KEY must be exactly 32 characters');
  process.exit(1);
}

console.log('Environment validation passed');
```

## Network and Connectivity

### RPC Endpoint Issues

**Health Check Script:**

```bash
#!/bin/bash
# rpc-health-check.sh

RPC_URLS=(
  "https://api.mainnet-beta.solana.com"
  "https://solana-api.projectserum.com"
  "https://rpc.ankr.com/solana"
)

for url in "${RPC_URLS[@]}"; do
  echo "Testing $url..."
  response=$(curl -s -X POST $url \
    -H "Content-Type: application/json" \
    -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' \
    -w "%{http_code}")
  
  if [[ $response == *"200"* ]]; then
    echo "✅ $url is healthy"
  else
    echo "❌ $url is not responding"
  fi
done
```

### Firewall and Security

**Common Ports:**
- MongoDB: 27017
- Application: 3001
- Nginx: 80, 443
- Solana RPC: 443 (HTTPS)

**Test Connectivity:**

```bash
# Test outbound connections
telnet api.mainnet-beta.solana.com 443
nc -zv your-mongodb-host 27017

# Check firewall rules (Ubuntu/Debian)
sudo ufw status
sudo iptables -L

# Check DNS resolution
nslookup api.mainnet-beta.solana.com
```

## Debugging Techniques

### Enable Debug Logging

```bash
# Set debug environment
export DEBUG=raydium:*,app:*
export LOG_LEVEL=debug

# Start with verbose logging
npm start 2>&1 | tee debug.log
```

### Request Tracing

```javascript
// Add request ID middleware
const { v4: uuidv4 } = require('uuid');

app.use((req, res, next) => {
  req.id = uuidv4();
  res.setHeader('X-Request-ID', req.id);
  console.log(`[${req.id}] ${req.method} ${req.path}`);
  next();
});
```

### Database Query Debugging

```javascript
// Enable MongoDB query logging
mongoose.set('debug', true);

// Custom query logging
const originalExec = mongoose.Query.prototype.exec;
mongoose.Query.prototype.exec = function() {
  console.log('Query:', this.getQuery());
  console.log('Collection:', this.mongooseCollection.name);
  return originalExec.apply(this, arguments);
};
```

## Frequently Asked Questions

### Q: Why am I getting "launchpad functionality requires specific API implementation" errors?

**A:** This is expected behavior. The current implementation includes placeholder functionality while the actual Raydium launchpad API methods are being researched and implemented. The system validates inputs and provides proper error handling but doesn't execute real transactions yet.

### Q: How do I switch between PumpFun and Raydium platforms?

**A:** Use the `isPump` parameter in API requests:
- `isPump: true` → Routes to PumpFun
- `isPump: false` → Routes to Raydium

The frontend provides a platform selection UI that automatically sets this parameter.

### Q: What's the difference in slippage handling between platforms?

**A:** 
- **PumpFun**: Fixed 10% slippage protection
- **Raydium**: User-configurable slippage (default 10%, adjustable in UI)

### Q: How do I optimize performance for large wallet batches?

**A:** 
- Use appropriate `MAX_CONCURRENT_OPERATIONS` setting
- Implement proper bundling with `BUNDLE_SIZE_LIMIT`
- Monitor memory usage and adjust batch sizes
- Use multiple RPC endpoints for load distribution

### Q: Can I run both platforms simultaneously?

**A:** Yes, the system supports concurrent operations on both platforms. Each request is routed independently based on the `isPump` parameter.

### Q: How do I handle rate limiting?

**A:** The system includes automatic retry logic with exponential backoff. Configure `RAYDIUM_MAX_RETRIES` and `RAYDIUM_RETRY_DELAY` for optimal performance.

### Q: What are the minimum system requirements?

**A:**
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 20GB free space
- **Network**: Stable connection with low latency to Solana RPC
- **Node.js**: Version 18.x or higher

### Q: How do I backup and restore the database?

**A:**
```bash
# Backup
mongodump --uri="$MONGODB_URI" --out=backup-$(date +%Y%m%d)

# Restore
mongorestore --uri="$MONGODB_URI" --drop backup-folder/
```

## Getting Help

### Log Collection

When reporting issues, include:

```bash
# System information
node --version
npm --version
uname -a

# Application logs
pm2 logs pumpfun-backend --lines 100

# Configuration (sanitized)
env | grep -E "(NODE_ENV|SOLANA_|RAYDIUM_)" | sed 's/=.*/=***/'

# Error details
curl -X POST http://localhost:3001/api/health/raydium
```

### Support Channels

1. **GitHub Issues**: For bug reports and feature requests
2. **Documentation**: Check API docs and configuration guide
3. **Development Team**: Contact for critical production issues

### Before Contacting Support

1. Check this troubleshooting guide
2. Review recent changes to configuration
3. Test with minimal configuration
4. Collect relevant logs and error messages
5. Document steps to reproduce the issue

For additional help, refer to the API documentation, configuration guide, and deployment documentation.
