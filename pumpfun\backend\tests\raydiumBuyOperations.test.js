// Unit tests for Raydium buy operations
const { buyRaydium } = require('../utils/raydiumBuyOperations');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');

const raydiumConfig = require('../utils/raydiumConfig');
const transactionUtils = require('../utils/transactionUtils');

describe('Raydium Buy Operations', () => {
  const mockWallets = [
    {
      encryptedPrivateKey: 'encrypted_key_1'
    },
    {
      encryptedPrivateKey: 'encrypted_key_2'
    }
  ];

  const mockAmounts = {
    'MockPublicKey': '0.1'
  };

  const mockToken = 'TokenMintAddress123';
  const mockJitoTip = '0.0001';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Raydium SDK initialization
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    
    // Mock logging functions
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockReturnValue({
      category: 'UNKNOWN_ERROR',
      severity: 'HIGH',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    });

    // Mock transaction utilities
    transactionUtils.getLatestBlockhashWithRetry.mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    });
    transactionUtils.submitTransactionToJito.mockResolvedValue('mock_jito_signature');
  });

  describe('buyRaydium function', () => {
    test('should handle successful buy operation', async () => {
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(raydiumConfig.getRaydiumSdkWithInit).toHaveBeenCalled();
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Starting Raydium launchpad buy operation')
      );
      expect(result).toEqual({
        success: false, // Expected to fail due to placeholder implementation
        totalWallets: 2,
        successfulTxs: 0,
        failedTxs: 2,
        results: [],
        errors: expect.any(Array),
        duration: expect.any(Number),
        platform: 'raydium'
      });
    });

    test('should handle empty wallet list', async () => {
      const result = await buyRaydium([], {}, mockToken, mockJitoTip, false);

      expect(result).toEqual({
        success: true, // No wallets to process, so technically successful
        totalWallets: 0,
        successfulTxs: 0,
        failedTxs: 0,
        results: [],
        errors: [],
        duration: expect.any(Number),
        platform: 'raydium'
      });
    });

    test('should handle missing amount for wallet', async () => {
      const walletsWithMissingAmount = [mockWallets[0]];
      const incompleteAmounts = {}; // No amounts provided

      const result = await buyRaydium(walletsWithMissingAmount, incompleteAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(1);
      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          error: expect.stringContaining('No amount specified for wallet'),
          walletIndex: 1
        })
      );
    });

    test('should handle SDK initialization failure', async () => {
      const sdkError = new Error('SDK initialization failed');
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(sdkError);

      await expect(buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      expect(raydiumConfig.handleRaydiumError).toHaveBeenCalledWith(
        sdkError,
        'Overall Raydium buy operation failed'
      );
    });

    test('should handle placeholder implementation error correctly', async () => {
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      // All wallets should fail due to placeholder implementation
      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(mockWallets.length);
      
      // Check that each error contains the expected placeholder message
      result.errors.forEach(error => {
        expect(error.error).toContain('launchpad functionality requires specific API implementation');
        expect(error.errorCategory).toBe('UNKNOWN_ERROR');
        expect(error.retryable).toBe(false);
      });
    });

    test('should log wallet processing details', async () => {
      await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Processing wallets:'),
        expect.any(Array)
      );
      
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Raydium SDK initialized successfully')
      );
    });

    test('should handle different jito tip amounts', async () => {
      // Test with zero jito tip
      const resultNoTip = await buyRaydium(mockWallets, mockAmounts, mockToken, '0', false);
      expect(resultNoTip.platform).toBe('raydium');

      // Test with higher jito tip
      const resultHighTip = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);
      expect(resultHighTip.platform).toBe('raydium');
    });

    test('should track operation duration', async () => {
      const startTime = Date.now();
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);
      const endTime = Date.now();

      expect(result.duration).toBeGreaterThan(0);
      expect(result.duration).toBeLessThan(endTime - startTime + 100); // Allow some margin
    });

    test('should handle concurrent wallet processing', async () => {
      const manyWallets = Array(5).fill().map((_, i) => ({
        encryptedPrivateKey: `encrypted_key_${i}`
      }));
      
      const manyAmounts = {};
      manyWallets.forEach((_, i) => {
        manyAmounts['MockPublicKey'] = '0.1';
      });

      const result = await buyRaydium(manyWallets, manyAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(5);
      expect(result.failedTxs).toBe(5); // All should fail due to placeholder
    });

    test('should properly format error responses', async () => {
      const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          walletIndex: 1,
          walletPublicKey: 'MockPublicKey',
          error: expect.any(String),
          errorCategory: expect.any(String),
          errorSeverity: expect.any(String),
          retryable: expect.any(Boolean),
          userMessage: expect.any(String),
          amount: '0.1',
          duration: expect.any(Number),
          success: false
        })
      );
    });

    test('should validate input parameters', async () => {
      // Test with null wallets
      await expect(buyRaydium(null, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      // Test with undefined amounts
      await expect(buyRaydium(mockWallets, undefined, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });

    test('should handle enhanced error categorization', async () => {
      raydiumConfig.handleRaydiumError.mockReturnValue({
        category: 'NETWORK_ERROR',
        severity: 'HIGH',
        retryable: true,
        userMessage: 'Network connection issue. Please try again.'
      });

      const result = await buyRaydium([mockWallets[0]], mockAmounts, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          errorCategory: 'NETWORK_ERROR',
          errorSeverity: 'HIGH',
          retryable: true,
          userMessage: 'Network connection issue. Please try again.'
        })
      );
    });
  });

  describe('error handling edge cases', () => {
    test('should handle Promise.allSettled rejection scenarios', async () => {
      // Mock a scenario where Promise.allSettled handles rejections
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      // Should still return a valid result structure even with failures
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('totalWallets');
      expect(result).toHaveProperty('successfulTxs');
      expect(result).toHaveProperty('failedTxs');
      expect(result).toHaveProperty('results');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('duration');
      expect(result).toHaveProperty('platform');
    });

    test('should handle malformed wallet data', async () => {
      const malformedWallets = [
        { /* missing encryptedPrivateKey */ },
        { encryptedPrivateKey: null },
        { encryptedPrivateKey: '' }
      ];

      const result = await buyRaydium(malformedWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(3);
    });
  });
});
