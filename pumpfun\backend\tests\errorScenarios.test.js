// Comprehensive error scenario and edge case tests for Raydium integration
const { buyRaydium } = require('../utils/raydiumBuyOperations');
const { sellRaydium } = require('../utils/raydiumSellOperations');
const { categorizeRaydiumError, handleRaydiumError } = require('../utils/raydiumConfig');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');
jest.mock('../utils/config');

const raydiumConfig = require('../utils/raydiumConfig');
const transactionUtils = require('../utils/transactionUtils');
const config = require('../utils/config');

describe('Error Scenarios and Edge Cases', () => {
  const mockWallets = [
    { encryptedPrivateKey: 'encrypted_key_1' },
    { encryptedPrivateKey: 'encrypted_key_2' }
  ];

  const mockAmounts = {
    'MockPublicKey': '0.1'
  };

  const mockToken = 'TokenMintAddress123';
  const mockJitoTip = '0.0001';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset to default mocks
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockImplementation((error, context, walletIndex) => {
      return categorizeRaydiumError(error);
    });

    config.getTokenDecimals.mockResolvedValue(9);
    config.connection = global.testUtils.generateMockConnection();
    
    transactionUtils.getLatestBlockhashWithRetry.mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    });
  });

  describe('Network error scenarios', () => {
    test('should handle connection timeouts', async () => {
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(
        global.testUtils.generateMockError('NETWORK_ERROR')
      );

      await expect(buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      expect(raydiumConfig.handleRaydiumError).toHaveBeenCalledWith(
        expect.objectContaining({ code: 'NETWORK_ERROR' }),
        'Overall Raydium buy operation failed'
      );
    });

    test('should handle RPC node failures', async () => {
      config.connection.getLatestBlockhash.mockRejectedValue(
        new Error('RPC node unavailable')
      );
      transactionUtils.getLatestBlockhashWithRetry.mockRejectedValue(
        new Error('All RPC nodes failed')
      );

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle intermittent network issues', async () => {
      let callCount = 0;
      raydiumConfig.getRaydiumSdkWithInit.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw global.testUtils.generateMockError('NETWORK_ERROR');
        }
        return Promise.resolve(global.testUtils.generateMockRaydiumSdk());
      });

      // Should retry and eventually succeed (though still fail due to placeholder)
      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);
      expect(result.totalWallets).toBe(2);
    });
  });

  describe('Insufficient funds scenarios', () => {
    test('should handle insufficient SOL for transaction fees', async () => {
      config.connection.getBalance.mockResolvedValue(1000); // Very low balance

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle insufficient SOL for buy amount', async () => {
      const largeAmounts = {
        'MockPublicKey': '1000' // 1000 SOL - unrealistic amount
      };

      const result = await buyRaydium(mockWallets, largeAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle insufficient token balance for sell', async () => {
      config.connection.getTokenAccountsByOwner.mockResolvedValue({
        value: [{ pubkey: 'token_account' }]
      });
      config.connection.getAccountInfo.mockResolvedValue({
        lamports: 0 // No tokens to sell
      });

      const result = await sellRaydium(mockWallets, 50, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.errors.some(error => error.error.includes('Token balance is 0'))).toBe(true);
    });
  });

  describe('Slippage and price impact scenarios', () => {
    test('should handle high slippage errors', async () => {
      raydiumConfig.handleRaydiumError.mockReturnValue({
        category: 'SLIPPAGE_ERROR',
        severity: 'MEDIUM',
        retryable: true,
        userMessage: 'Price changed during transaction. Please try again.'
      });

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          errorCategory: 'SLIPPAGE_ERROR',
          retryable: true
        })
      );
    });

    test('should handle price impact too high scenarios', async () => {
      const largeAmounts = {
        'MockPublicKey': '10' // Large amount that might cause high price impact
      };

      const result = await buyRaydium(mockWallets, largeAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });
  });

  describe('Rate limiting scenarios', () => {
    test('should handle API rate limiting', async () => {
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(
        global.testUtils.generateMockError('RATE_LIMITED')
      );

      await expect(buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      expect(raydiumConfig.handleRaydiumError).toHaveBeenCalledWith(
        expect.objectContaining({ code: 'RATE_LIMITED' }),
        'Overall Raydium buy operation failed'
      );
    });

    test('should handle transaction rate limiting', async () => {
      transactionUtils.submitTransactionToJito.mockRejectedValue(
        new Error('Rate limit exceeded')
      );

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, '0.001', false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });
  });

  describe('SDK and initialization errors', () => {
    test('should handle SDK initialization failures', async () => {
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(
        global.testUtils.generateMockError('SDK_ERROR')
      );

      await expect(buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });

    test('should handle SDK method not available errors', async () => {
      const incompleteSdk = {
        // Missing expected methods
      };
      raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(incompleteSdk);

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle SDK version incompatibility', async () => {
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(
        new Error('SDK version incompatible')
      );

      await expect(sellRaydium(mockWallets, 50, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });
  });

  describe('Transaction building errors', () => {
    test('should handle invalid transaction parameters', async () => {
      const invalidAmounts = {
        'MockPublicKey': 'invalid_amount' // Non-numeric amount
      };

      const result = await buyRaydium(mockWallets, invalidAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle invalid token mint addresses', async () => {
      const invalidToken = 'invalid_token_address';

      const result = await buyRaydium(mockWallets, mockAmounts, invalidToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(2);
    });

    test('should handle transaction size limits', async () => {
      // Mock a scenario where transaction becomes too large
      const manyWallets = Array(100).fill().map((_, i) => ({
        encryptedPrivateKey: `key_${i}`
      }));

      const manyAmounts = {};
      manyWallets.forEach((_, i) => {
        manyAmounts['MockPublicKey'] = '0.1';
      });

      const result = await buyRaydium(manyWallets, manyAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(100);
      // Should handle large batches gracefully
    });
  });

  describe('Data validation edge cases', () => {
    test('should handle null and undefined inputs', async () => {
      await expect(buyRaydium(null, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      await expect(buyRaydium(mockWallets, null, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      await expect(buyRaydium(mockWallets, mockAmounts, null, mockJitoTip, false))
        .rejects.toThrow();
    });

    test('should handle empty arrays and objects', async () => {
      const emptyResult = await buyRaydium([], {}, mockToken, mockJitoTip, false);
      expect(emptyResult.success).toBe(true);
      expect(emptyResult.totalWallets).toBe(0);

      const noAmountsResult = await buyRaydium(mockWallets, {}, mockToken, mockJitoTip, false);
      expect(noAmountsResult.success).toBe(false);
    });

    test('should handle malformed wallet objects', async () => {
      const malformedWallets = [
        {}, // Empty object
        { publicKey: 'test' }, // Missing encryptedPrivateKey
        { encryptedPrivateKey: '' }, // Empty encryptedPrivateKey
        null, // Null wallet
        'invalid_wallet' // String instead of object
      ];

      const result = await buyRaydium(malformedWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(5);
    });

    test('should handle extreme numeric values', async () => {
      const extremeAmounts = {
        'MockPublicKey': '0.000000001' // Very small amount
      };

      const result = await buyRaydium(mockWallets, extremeAmounts, mockToken, mockJitoTip, false);
      expect(result.totalWallets).toBe(2);

      const negativeAmounts = {
        'MockPublicKey': '-1' // Negative amount
      };

      const negativeResult = await buyRaydium(mockWallets, negativeAmounts, mockToken, mockJitoTip, false);
      expect(negativeResult.success).toBe(false);
    });
  });

  describe('Concurrent operation errors', () => {
    test('should handle concurrent SDK initialization', async () => {
      let initCount = 0;
      raydiumConfig.getRaydiumSdkWithInit.mockImplementation(() => {
        initCount++;
        if (initCount <= 2) {
          return new Promise(resolve => 
            setTimeout(() => resolve(global.testUtils.generateMockRaydiumSdk()), 100)
          );
        }
        throw new Error('Too many concurrent initializations');
      });

      const promises = [
        buyRaydium([mockWallets[0]], mockAmounts, mockToken, mockJitoTip, false),
        buyRaydium([mockWallets[1]], mockAmounts, mockToken, mockJitoTip, false),
        sellRaydium([mockWallets[0]], 50, mockToken, mockJitoTip, false)
      ];

      const results = await Promise.allSettled(promises);
      
      // At least some operations should complete
      expect(results.some(r => r.status === 'fulfilled')).toBe(true);
    });

    test('should handle resource contention', async () => {
      // Mock scenario where multiple operations compete for resources
      let resourceLocked = false;
      
      raydiumConfig.getRaydiumSdkWithInit.mockImplementation(() => {
        if (resourceLocked) {
          throw new Error('Resource temporarily unavailable');
        }
        resourceLocked = true;
        setTimeout(() => { resourceLocked = false; }, 50);
        return Promise.resolve(global.testUtils.generateMockRaydiumSdk());
      });

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);
      expect(result.totalWallets).toBe(2);
    });
  });

  describe('Recovery and retry scenarios', () => {
    test('should handle transient errors with retry', async () => {
      let attemptCount = 0;
      transactionUtils.getLatestBlockhashWithRetry.mockImplementation(() => {
        attemptCount++;
        if (attemptCount === 1) {
          throw new Error('Transient error');
        }
        return Promise.resolve({
          blockhash: 'mock_blockhash',
          lastValidBlockHeight: 123456
        });
      });

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);
      expect(result.totalWallets).toBe(2);
    });

    test('should handle permanent failures gracefully', async () => {
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(
        new Error('Permanent SDK failure')
      );

      await expect(buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false))
        .rejects.toThrow('Permanent SDK failure');
    });

    test('should maintain state consistency during failures', async () => {
      // Mock partial failure scenario
      let walletCount = 0;
      const originalDecrypt = require('../utils/crypto').decrypt;
      
      require('../utils/crypto').decrypt.mockImplementation(() => {
        walletCount++;
        if (walletCount === 2) {
          throw new Error('Decryption failed for wallet 2');
        }
        return 'decrypted_key';
      });

      const result = await buyRaydium(mockWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(2);
      expect(result.failedTxs).toBe(2);
      // Should maintain consistent error reporting
    });
  });
});
