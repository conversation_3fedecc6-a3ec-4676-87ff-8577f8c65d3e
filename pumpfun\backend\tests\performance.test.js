// Performance testing and optimization tests for Raydium integration
const { buyRaydium } = require('../utils/raydiumBuyOperations');
const { sellRaydium } = require('../utils/raydiumSellOperations');
const { initializeRaydiumSdk } = require('../utils/raydiumConfig');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');
jest.mock('../utils/config');

const raydiumConfig = require('../utils/raydiumConfig');
const transactionUtils = require('../utils/transactionUtils');
const config = require('../utils/config');

describe('Performance Testing and Optimization', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock fast responses for performance testing
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockReturnValue({
      category: 'UNKNOWN_ERROR',
      severity: 'HIGH',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    });

    config.getTokenDecimals.mockResolvedValue(9);
    config.connection = global.testUtils.generateMockConnection();
    
    transactionUtils.getLatestBlockhashWithRetry.mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    });
    transactionUtils.submitTransactionToJito.mockResolvedValue('mock_signature');
  });

  describe('SDK initialization performance', () => {
    test('should initialize SDK within acceptable time', async () => {
      const startTime = Date.now();
      
      await raydiumConfig.getRaydiumSdkWithInit();
      
      const endTime = Date.now();
      const initTime = endTime - startTime;
      
      // SDK initialization should complete within 2 seconds
      expect(initTime).toBeLessThan(2000);
    });

    test('should cache SDK instance for subsequent calls', async () => {
      const startTime1 = Date.now();
      await raydiumConfig.getRaydiumSdkWithInit();
      const endTime1 = Date.now();
      const firstCallTime = endTime1 - startTime1;

      const startTime2 = Date.now();
      await raydiumConfig.getRaydiumSdkWithInit();
      const endTime2 = Date.now();
      const secondCallTime = endTime2 - startTime2;

      // Second call should be significantly faster (cached)
      expect(secondCallTime).toBeLessThan(firstCallTime / 2);
      expect(raydiumConfig.getRaydiumSdkWithInit).toHaveBeenCalledTimes(2);
    });

    test('should handle concurrent initialization efficiently', async () => {
      const startTime = Date.now();
      
      const promises = Array(10).fill().map(() => 
        raydiumConfig.getRaydiumSdkWithInit()
      );
      
      await Promise.all(promises);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Concurrent initialization should not take much longer than single init
      expect(totalTime).toBeLessThan(3000);
    });
  });

  describe('Single wallet operation performance', () => {
    const singleWallet = [{ encryptedPrivateKey: 'encrypted_key_1' }];
    const singleAmount = { 'MockPublicKey': '0.1' };
    const mockToken = 'TokenMintAddress123';
    const mockJitoTip = '0.0001';

    test('should complete buy operation within time limit', async () => {
      const startTime = Date.now();
      
      const result = await buyRaydium(singleWallet, singleAmount, mockToken, mockJitoTip, false);
      
      const endTime = Date.now();
      const operationTime = endTime - startTime;
      
      // Single wallet buy should complete within 3 seconds
      expect(operationTime).toBeLessThan(3000);
      expect(result.duration).toBeGreaterThan(0);
      expect(result.duration).toBeLessThan(operationTime + 100);
    });

    test('should complete sell operation within time limit', async () => {
      const startTime = Date.now();
      
      const result = await sellRaydium(singleWallet, 50, mockToken, mockJitoTip, false);
      
      const endTime = Date.now();
      const operationTime = endTime - startTime;
      
      // Single wallet sell should complete within 3 seconds
      expect(operationTime).toBeLessThan(3000);
      expect(result.duration).toBeGreaterThan(0);
    });

    test('should maintain consistent performance across operations', async () => {
      const times = [];
      
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        await buyRaydium(singleWallet, singleAmount, mockToken, mockJitoTip, false);
        const endTime = Date.now();
        times.push(endTime - startTime);
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      // Performance should be consistent (max time shouldn't be more than 3x min time)
      expect(maxTime).toBeLessThan(minTime * 3);
      expect(avgTime).toBeLessThan(3000);
    });
  });

  describe('Multi-wallet operation performance', () => {
    const generateWallets = (count) => 
      Array(count).fill().map((_, i) => ({ encryptedPrivateKey: `encrypted_key_${i}` }));
    
    const generateAmounts = (count) => {
      const amounts = {};
      for (let i = 0; i < count; i++) {
        amounts['MockPublicKey'] = '0.1';
      }
      return amounts;
    };

    test('should scale linearly with wallet count', async () => {
      const walletCounts = [1, 5, 10];
      const times = [];
      
      for (const count of walletCounts) {
        const wallets = generateWallets(count);
        const amounts = generateAmounts(count);
        
        const startTime = Date.now();
        await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
        const endTime = Date.now();
        
        times.push({ count, time: endTime - startTime });
      }
      
      // Performance should scale reasonably (not exponentially)
      const timePerWallet1 = times[0].time / times[0].count;
      const timePerWallet10 = times[2].time / times[2].count;
      
      // Time per wallet for 10 wallets shouldn't be more than 2x time per wallet for 1 wallet
      expect(timePerWallet10).toBeLessThan(timePerWallet1 * 2);
    });

    test('should handle large batches efficiently', async () => {
      const largeWalletCount = 50;
      const wallets = generateWallets(largeWalletCount);
      const amounts = generateAmounts(largeWalletCount);
      
      const startTime = Date.now();
      const result = await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
      const endTime = Date.now();
      
      const operationTime = endTime - startTime;
      const timePerWallet = operationTime / largeWalletCount;
      
      // Should complete within 30 seconds for 50 wallets
      expect(operationTime).toBeLessThan(30000);
      // Average time per wallet should be reasonable
      expect(timePerWallet).toBeLessThan(600); // 600ms per wallet max
      expect(result.totalWallets).toBe(largeWalletCount);
    });

    test('should maintain performance under concurrent operations', async () => {
      const wallets = generateWallets(5);
      const amounts = generateAmounts(5);
      
      const startTime = Date.now();
      
      const promises = [
        buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false),
        sellRaydium(wallets, 50, 'TokenMintAddress123', '0.0001', false),
        buyRaydium(wallets, amounts, 'TokenMintAddress456', '0.0001', false)
      ];
      
      const results = await Promise.all(promises);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Concurrent operations should complete within 10 seconds
      expect(totalTime).toBeLessThan(10000);
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.totalWallets).toBe(5);
      });
    });
  });

  describe('Memory usage optimization', () => {
    test('should not leak memory during operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple operations
      for (let i = 0; i < 10; i++) {
        const wallets = [{ encryptedPrivateKey: `key_${i}` }];
        const amounts = { 'MockPublicKey': '0.1' };
        
        await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    test('should handle large data sets without excessive memory usage', async () => {
      const largeWalletCount = 100;
      const wallets = generateWallets(largeWalletCount);
      const amounts = generateAmounts(largeWalletCount);
      
      const initialMemory = process.memoryUsage().heapUsed;
      
      await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be proportional to data size (less than 100MB for 100 wallets)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);
    });
  });

  describe('Error handling performance', () => {
    test('should fail fast on invalid inputs', async () => {
      const startTime = Date.now();
      
      try {
        await buyRaydium(null, {}, 'TokenMintAddress123', '0.0001', false);
      } catch (error) {
        // Expected to fail
      }
      
      const endTime = Date.now();
      const errorTime = endTime - startTime;
      
      // Should fail within 100ms for invalid inputs
      expect(errorTime).toBeLessThan(100);
    });

    test('should handle errors efficiently without blocking', async () => {
      // Mock SDK to throw errors
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(new Error('SDK Error'));
      
      const startTime = Date.now();
      
      try {
        await buyRaydium([{ encryptedPrivateKey: 'key' }], { 'MockPublicKey': '0.1' }, 'TokenMintAddress123', '0.0001', false);
      } catch (error) {
        // Expected to fail
      }
      
      const endTime = Date.now();
      const errorTime = endTime - startTime;
      
      // Error handling should be fast
      expect(errorTime).toBeLessThan(1000);
    });
  });

  describe('Optimization benchmarks', () => {
    test('should meet performance benchmarks for typical usage', async () => {
      const benchmarks = {
        singleWalletBuy: 2000,    // 2 seconds
        singleWalletSell: 2000,   // 2 seconds
        tenWalletBuy: 8000,       // 8 seconds
        tenWalletSell: 8000,      // 8 seconds
      };
      
      // Single wallet buy benchmark
      const singleWallet = [{ encryptedPrivateKey: 'key_1' }];
      const singleAmount = { 'MockPublicKey': '0.1' };
      
      let startTime = Date.now();
      await buyRaydium(singleWallet, singleAmount, 'TokenMintAddress123', '0.0001', false);
      let endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(benchmarks.singleWalletBuy);
      
      // Single wallet sell benchmark
      startTime = Date.now();
      await sellRaydium(singleWallet, 50, 'TokenMintAddress123', '0.0001', false);
      endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(benchmarks.singleWalletSell);
      
      // Ten wallet buy benchmark
      const tenWallets = generateWallets(10);
      const tenAmounts = generateAmounts(10);
      
      startTime = Date.now();
      await buyRaydium(tenWallets, tenAmounts, 'TokenMintAddress123', '0.0001', false);
      endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(benchmarks.tenWalletBuy);
      
      // Ten wallet sell benchmark
      startTime = Date.now();
      await sellRaydium(tenWallets, 50, 'TokenMintAddress123', '0.0001', false);
      endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(benchmarks.tenWalletSell);
    });

    test('should compare favorably with PumpFun operations', async () => {
      const wallets = [{ encryptedPrivateKey: 'key_1' }];
      const amounts = { 'MockPublicKey': '0.1' };
      
      // Measure Raydium operation time
      const raydiumStartTime = Date.now();
      await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
      const raydiumEndTime = Date.now();
      const raydiumTime = raydiumEndTime - raydiumStartTime;
      
      // Raydium operations should be competitive (within reasonable range)
      // Since both are placeholder implementations, they should have similar performance
      expect(raydiumTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });

  describe('Resource utilization', () => {
    test('should use CPU efficiently', async () => {
      const wallets = generateWallets(20);
      const amounts = generateAmounts(20);
      
      const startTime = Date.now();
      const startCpuUsage = process.cpuUsage();
      
      await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
      
      const endTime = Date.now();
      const endCpuUsage = process.cpuUsage(startCpuUsage);
      
      const wallClockTime = endTime - startTime;
      const cpuTime = (endCpuUsage.user + endCpuUsage.system) / 1000; // Convert to milliseconds
      
      // CPU efficiency: CPU time should not be much higher than wall clock time
      // (accounting for some overhead and parallel processing)
      expect(cpuTime).toBeLessThan(wallClockTime * 2);
    });

    test('should handle network latency gracefully', async () => {
      // Mock network delay
      transactionUtils.getLatestBlockhashWithRetry.mockImplementation(() =>
        new Promise(resolve => 
          setTimeout(() => resolve({
            blockhash: 'mock_blockhash',
            lastValidBlockHeight: 123456
          }), 500) // 500ms delay
        )
      );
      
      const wallets = [{ encryptedPrivateKey: 'key_1' }];
      const amounts = { 'MockPublicKey': '0.1' };
      
      const startTime = Date.now();
      await buyRaydium(wallets, amounts, 'TokenMintAddress123', '0.0001', false);
      const endTime = Date.now();
      
      const totalTime = endTime - startTime;
      
      // Should handle network delay efficiently (not much more than the delay itself)
      expect(totalTime).toBeGreaterThan(500); // At least the network delay
      expect(totalTime).toBeLessThan(2000); // But not excessively longer
    });
  });

  // Helper function to generate test data
  const generateWallets = (count) => 
    Array(count).fill().map((_, i) => ({ encryptedPrivateKey: `encrypted_key_${i}` }));
  
  const generateAmounts = (count) => {
    const amounts = {};
    for (let i = 0; i < count; i++) {
      amounts['MockPublicKey'] = '0.1';
    }
    return amounts;
  };
});
