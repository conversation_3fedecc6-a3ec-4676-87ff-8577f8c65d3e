// Unit tests for Raydium sell operations
const { sellRaydium } = require('../utils/raydiumSellOperations');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');
jest.mock('../utils/config');

const raydiumConfig = require('../utils/raydiumConfig');
const transactionUtils = require('../utils/transactionUtils');
const config = require('../utils/config');

describe('Raydium Sell Operations', () => {
  const mockWallets = [
    {
      encryptedPrivateKey: 'encrypted_key_1'
    },
    {
      encryptedPrivateKey: 'encrypted_key_2'
    }
  ];

  const mockSellPercentage = 50; // Sell 50% of tokens
  const mockToken = 'TokenMintAddress123';
  const mockJitoTip = '0.0001';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Raydium SDK initialization
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    
    // Mock logging functions
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockReturnValue({
      category: 'UNKNOWN_ERROR',
      severity: 'HIGH',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    });

    // Mock config functions
    config.getTokenDecimals.mockResolvedValue(9);
    config.connection = global.testUtils.generateMockConnection();

    // Mock transaction utilities
    transactionUtils.getLatestBlockhashWithRetry.mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    });
    transactionUtils.submitTransactionToJito.mockResolvedValue('mock_jito_signature');
  });

  describe('sellRaydium function', () => {
    test('should handle successful sell operation setup', async () => {
      const result = await sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      expect(raydiumConfig.getRaydiumSdkWithInit).toHaveBeenCalled();
      expect(config.getTokenDecimals).toHaveBeenCalledWith(mockToken);
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Starting Raydium launchpad sell operation')
      );
      expect(result).toEqual({
        success: false, // Expected to fail due to placeholder implementation
        totalWallets: 2,
        successfulTxs: 0,
        failedTxs: 2,
        results: [],
        errors: expect.any(Array),
        duration: expect.any(Number),
        platform: 'raydium'
      });
    });

    test('should handle empty wallet list', async () => {
      const result = await sellRaydium([], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result).toEqual({
        success: true, // No wallets to process, so technically successful
        totalWallets: 0,
        successfulTxs: 0,
        failedTxs: 0,
        results: [],
        errors: [],
        duration: expect.any(Number),
        platform: 'raydium'
      });
    });

    test('should handle different sell percentages', async () => {
      // Test with 100% sell
      const result100 = await sellRaydium(mockWallets, 100, mockToken, mockJitoTip, false);
      expect(result100.platform).toBe('raydium');

      // Test with 25% sell
      const result25 = await sellRaydium(mockWallets, 25, mockToken, mockJitoTip, false);
      expect(result25.platform).toBe('raydium');

      // Test with 1% sell
      const result1 = await sellRaydium(mockWallets, 1, mockToken, mockJitoTip, false);
      expect(result1.platform).toBe('raydium');
    });

    test('should handle SDK initialization failure', async () => {
      const sdkError = new Error('SDK initialization failed');
      raydiumConfig.getRaydiumSdkWithInit.mockRejectedValue(sdkError);

      await expect(sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      expect(raydiumConfig.handleRaydiumError).toHaveBeenCalledWith(
        sdkError,
        'Overall Raydium sell operation failed'
      );
    });

    test('should handle token decimals retrieval failure', async () => {
      config.getTokenDecimals.mockRejectedValue(new Error('Failed to get token decimals'));

      await expect(sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });

    test('should handle placeholder implementation error correctly', async () => {
      const result = await sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      // All wallets should fail due to placeholder implementation
      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(mockWallets.length);
      
      // Check that each error contains the expected placeholder message
      result.errors.forEach(error => {
        expect(error.error).toContain('launchpad functionality requires specific API implementation');
        expect(error.errorCategory).toBe('UNKNOWN_ERROR');
        expect(error.retryable).toBe(false);
        expect(error.percentage).toBe(mockSellPercentage);
      });
    });

    test('should log wallet processing details', async () => {
      await sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Processing wallets:'),
        expect.any(Array)
      );
      
      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Raydium SDK initialized successfully')
      );

      expect(raydiumConfig.logWithTimestamp).toHaveBeenCalledWith(
        expect.stringContaining('Token decimals: 9')
      );
    });

    test('should handle no token account scenario', async () => {
      // Mock connection to return no token accounts
      config.connection.getTokenAccountsByOwner.mockResolvedValue({ value: [] });

      const result = await sellRaydium([mockWallets[0]], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          error: expect.stringContaining('No token account found'),
          success: false
        })
      );
    });

    test('should handle zero token balance scenario', async () => {
      // Mock connection to return token account with zero balance
      config.connection.getTokenAccountsByOwner.mockResolvedValue({
        value: [{ pubkey: 'mock_token_account' }]
      });
      config.connection.getAccountInfo.mockResolvedValue({
        lamports: 0 // Zero balance
      });

      const result = await sellRaydium([mockWallets[0]], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          error: 'Token balance is 0',
          success: false
        })
      );
    });

    test('should track operation duration', async () => {
      const startTime = Date.now();
      const result = await sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false);
      const endTime = Date.now();

      expect(result.duration).toBeGreaterThan(0);
      expect(result.duration).toBeLessThan(endTime - startTime + 100); // Allow some margin
    });

    test('should handle concurrent wallet processing', async () => {
      const manyWallets = Array(5).fill().map((_, i) => ({
        encryptedPrivateKey: `encrypted_key_${i}`
      }));

      const result = await sellRaydium(manyWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(5);
      expect(result.failedTxs).toBe(5); // All should fail due to placeholder
    });

    test('should properly format error responses', async () => {
      const result = await sellRaydium([mockWallets[0]], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          walletIndex: 1,
          walletPublicKey: 'MockPublicKey',
          error: expect.any(String),
          errorCategory: expect.any(String),
          errorSeverity: expect.any(String),
          retryable: expect.any(Boolean),
          userMessage: expect.any(String),
          percentage: mockSellPercentage,
          duration: expect.any(Number),
          success: false
        })
      );
    });

    test('should validate input parameters', async () => {
      // Test with null wallets
      await expect(sellRaydium(null, mockSellPercentage, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      // Test with invalid percentage
      await expect(sellRaydium(mockWallets, -1, mockToken, mockJitoTip, false))
        .rejects.toThrow();

      await expect(sellRaydium(mockWallets, 101, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });

    test('should handle enhanced error categorization', async () => {
      raydiumConfig.handleRaydiumError.mockReturnValue({
        category: 'SLIPPAGE_ERROR',
        severity: 'MEDIUM',
        retryable: true,
        userMessage: 'Price changed during transaction. Please try again.'
      });

      const result = await sellRaydium([mockWallets[0]], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          errorCategory: 'SLIPPAGE_ERROR',
          errorSeverity: 'MEDIUM',
          retryable: true,
          userMessage: 'Price changed during transaction. Please try again.'
        })
      );
    });

    test('should handle different jito tip amounts', async () => {
      // Test with zero jito tip
      const resultNoTip = await sellRaydium(mockWallets, mockSellPercentage, mockToken, '0', false);
      expect(resultNoTip.platform).toBe('raydium');

      // Test with higher jito tip
      const resultHighTip = await sellRaydium(mockWallets, mockSellPercentage, mockToken, '0.001', false);
      expect(resultHighTip.platform).toBe('raydium');
    });
  });

  describe('error handling edge cases', () => {
    test('should handle Promise.allSettled rejection scenarios', async () => {
      const result = await sellRaydium(mockWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      // Should still return a valid result structure even with failures
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('totalWallets');
      expect(result).toHaveProperty('successfulTxs');
      expect(result).toHaveProperty('failedTxs');
      expect(result).toHaveProperty('results');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('duration');
      expect(result).toHaveProperty('platform');
    });

    test('should handle malformed wallet data', async () => {
      const malformedWallets = [
        { /* missing encryptedPrivateKey */ },
        { encryptedPrivateKey: null },
        { encryptedPrivateKey: '' }
      ];

      const result = await sellRaydium(malformedWallets, mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(3);
    });

    test('should handle getAccountInfo failure', async () => {
      config.connection.getTokenAccountsByOwner.mockResolvedValue({
        value: [{ pubkey: 'mock_token_account' }]
      });
      config.connection.getAccountInfo.mockResolvedValue(null);

      const result = await sellRaydium([mockWallets[0]], mockSellPercentage, mockToken, mockJitoTip, false);

      expect(result.errors[0]).toEqual(
        expect.objectContaining({
          error: expect.stringContaining('Failed to get token account info'),
          success: false
        })
      );
    });
  });
});
