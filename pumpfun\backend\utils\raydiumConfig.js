const {
  Connection,
  Keypair,
  PublicKey,
} = require("@solana/web3.js");
const { Raydium } = require("@raydium-io/raydium-sdk-v2");
const { AnchorProvider, Wallet } = require("@coral-xyz/anchor");

// Import shared configuration from main config
const { connection, jitoTipAccounts, delay } = require("./config");

// Raydium Launchpad specific constants
// Based on the LaunchLab documentation, these are the program IDs
const RAYDIUM_LAUNCHPAD_PROGRAM_ID = {
  mainnet: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eAqKxYDiNP", // From documentation
  devnet: "LanD8FpTBBvzZFXjTxsAoipkFsxPUCDB4qAqKxYDiNP"   // From documentation
};

// Configuration constants for Raydium operations
const RAYDIUM_CONFIG = {
  MAX_TRANSACTIONS_PER_BUNDLE: 3,
  RATE_LIMIT_RETRY_DELAY: 1000,
  MAX_RATE_LIMIT_RETRIES: 2,
  PRIORITY_FEE_UNIT_LIMIT: 100000,
  PRIORITY_FEE_UNIT_PRICE: 25000,
  DEFAULT_SLIPPAGE_BPS: 1000, // 10% slippage in basis points
};

// Determine which program ID to use based on environment
const isDevnet = process.env.SOLANA_NETWORK === 'devnet';
const raydiumLaunchpadProgramId = new PublicKey(
  isDevnet ? RAYDIUM_LAUNCHPAD_PROGRAM_ID.devnet : RAYDIUM_LAUNCHPAD_PROGRAM_ID.mainnet
);

// Create a dedicated keypair for Raydium operations
const raydiumKeyPair = Keypair.generate();
const raydiumWallet = new Wallet(raydiumKeyPair);
const raydiumAnchorProvider = new AnchorProvider(connection, raydiumWallet, {
  commitment: "confirmed",
  preflightCommitment: "processed"
});

// Raydium SDK instance (will be initialized lazily)
let raydiumSdk = null;
let initializationPromise = null;

/**
 * Initialize Raydium SDK V2 with launchpad support
 * This function ensures the SDK is only initialized once and handles concurrent calls
 */
const initializeRaydiumSdk = async () => {
  // If already initialized, return the existing instance
  if (raydiumSdk) {
    return raydiumSdk;
  }

  // If initialization is in progress, wait for it
  if (initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  initializationPromise = (async () => {
    try {
      console.log('[RAYDIUM-CONFIG] Initializing Raydium SDK V2...');
      
      raydiumSdk = await Raydium.load({
        connection,
        owner: raydiumKeyPair, // Use dedicated keypair for Raydium operations
        disableLoadToken: false, // Enable token loading for launchpad operations
        // Add cluster configuration if needed
        cluster: isDevnet ? 'devnet' : 'mainnet-beta'
      });

      console.log('[RAYDIUM-CONFIG] Raydium SDK V2 initialized successfully');
      console.log(`[RAYDIUM-CONFIG] Using ${isDevnet ? 'devnet' : 'mainnet'} configuration`);
      console.log(`[RAYDIUM-CONFIG] Launchpad Program ID: ${raydiumLaunchpadProgramId.toBase58()}`);
      
      return raydiumSdk;
    } catch (error) {
      console.error('[RAYDIUM-CONFIG] Failed to initialize Raydium SDK V2:', error);
      // Reset the promise so we can retry
      initializationPromise = null;
      raydiumSdk = null;
      throw error;
    }
  })();

  return initializationPromise;
};

/**
 * Get the initialized Raydium SDK instance
 * Throws an error if not initialized
 */
const getRaydiumSdk = () => {
  if (!raydiumSdk) {
    throw new Error('Raydium SDK not initialized. Call initializeRaydiumSdk() first.');
  }
  return raydiumSdk;
};

/**
 * Get Raydium SDK with automatic initialization
 * This is a convenience function that initializes the SDK if needed
 */
const getRaydiumSdkWithInit = async () => {
  if (!raydiumSdk) {
    await initializeRaydiumSdk();
  }
  return raydiumSdk;
};

/**
 * Helper function to log with timestamps (Raydium-specific)
 */
const logWithTimestamp = (message, data = null) => {
  const timestamp = new Date().toISOString();
  if (data) {
    console.log(`[${timestamp}] RAYDIUM: ${message}`, data);
  } else {
    console.log(`[${timestamp}] RAYDIUM: ${message}`);
  }
};

/**
 * Helper function to log errors with timestamps (Raydium-specific)
 */
const logErrorWithTimestamp = (message, error) => {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] RAYDIUM ERROR: ${message}`, error);

  // Log additional details if available
  if (error?.response?.data) {
    console.error(`[${timestamp}] Response data:`, error.response.data);
  }

  // Log stack trace for debugging
  if (error?.stack) {
    console.error(`[${timestamp}] Stack trace:`, error.stack);
  }

  // Log Raydium-specific error details
  if (error?.code) {
    console.error(`[${timestamp}] Error code:`, error.code);
  }

  if (error?.programErrorStack) {
    console.error(`[${timestamp}] Program error stack:`, error.programErrorStack);
  }

  // Log transaction-related errors
  if (error?.transactionMessage) {
    console.error(`[${timestamp}] Transaction message:`, error.transactionMessage);
  }

  if (error?.logs) {
    console.error(`[${timestamp}] Transaction logs:`, error.logs);
  }
};

/**
 * Handle rate limiting for Raydium operations
 */
const handleRaydiumRateLimit = async (retryCount) => {
  const constantDelay = RAYDIUM_CONFIG.RATE_LIMIT_RETRY_DELAY;
  logWithTimestamp(`Rate limit hit. Retrying in ${constantDelay}ms (attempt ${retryCount + 1}/${RAYDIUM_CONFIG.MAX_RATE_LIMIT_RETRIES})`);
  await delay(constantDelay);
};

/**
 * Categorize errors for better handling and reporting
 */
const categorizeRaydiumError = (error) => {
  const errorMessage = error?.message?.toLowerCase() || '';
  const errorCode = error?.code;

  // Network-related errors
  if (errorMessage.includes('network') || errorMessage.includes('connection') ||
      errorMessage.includes('timeout') || errorCode === 'NETWORK_ERROR') {
    return {
      category: 'NETWORK_ERROR',
      severity: 'HIGH',
      retryable: true,
      userMessage: 'Network connection issue. Please try again.'
    };
  }

  // Insufficient funds errors
  if (errorMessage.includes('insufficient') || errorMessage.includes('balance') ||
      errorCode === 'INSUFFICIENT_FUNDS') {
    return {
      category: 'INSUFFICIENT_FUNDS',
      severity: 'MEDIUM',
      retryable: false,
      userMessage: 'Insufficient funds for this transaction.'
    };
  }

  // Slippage/price impact errors
  if (errorMessage.includes('slippage') || errorMessage.includes('price impact') ||
      errorMessage.includes('minimum received')) {
    return {
      category: 'SLIPPAGE_ERROR',
      severity: 'MEDIUM',
      retryable: true,
      userMessage: 'Price changed during transaction. Please try again.'
    };
  }

  // Rate limiting errors
  if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests') ||
      errorCode === 'RATE_LIMITED') {
    return {
      category: 'RATE_LIMITED',
      severity: 'LOW',
      retryable: true,
      userMessage: 'Too many requests. Please wait a moment and try again.'
    };
  }

  // Transaction errors
  if (errorMessage.includes('transaction') || errorMessage.includes('simulation failed') ||
      errorCode === 'TRANSACTION_ERROR') {
    return {
      category: 'TRANSACTION_ERROR',
      severity: 'HIGH',
      retryable: true,
      userMessage: 'Transaction failed. Please try again.'
    };
  }

  // SDK initialization errors
  if (errorMessage.includes('initialization') || errorMessage.includes('sdk') ||
      errorCode === 'SDK_ERROR') {
    return {
      category: 'SDK_ERROR',
      severity: 'CRITICAL',
      retryable: false,
      userMessage: 'System initialization error. Please contact support.'
    };
  }

  // Default category for unknown errors
  return {
    category: 'UNKNOWN_ERROR',
    severity: 'HIGH',
    retryable: false,
    userMessage: 'An unexpected error occurred. Please try again or contact support.'
  };
};

/**
 * Enhanced error handler with categorization and logging
 */
const handleRaydiumError = (error, context = '', walletIndex = null) => {
  const errorCategory = categorizeRaydiumError(error);
  const contextPrefix = walletIndex ? `[Wallet ${walletIndex}] ` : '';

  logErrorWithTimestamp(
    `${contextPrefix}${context} - ${errorCategory.category} (${errorCategory.severity})`,
    {
      originalError: error.message,
      category: errorCategory.category,
      severity: errorCategory.severity,
      retryable: errorCategory.retryable,
      userMessage: errorCategory.userMessage,
      context
    }
  );

  return errorCategory;
};

/**
 * Get platform configuration for Raydium launchpad
 * This would be used for setting up platform-specific parameters
 */
const getRaydiumPlatformConfig = () => {
  return {
    programId: raydiumLaunchpadProgramId,
    network: isDevnet ? 'devnet' : 'mainnet-beta',
    slippageBps: RAYDIUM_CONFIG.DEFAULT_SLIPPAGE_BPS,
    priorityFee: {
      unitLimit: RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_LIMIT,
      unitPrice: RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_PRICE
    },
    bundling: {
      maxTransactionsPerBundle: RAYDIUM_CONFIG.MAX_TRANSACTIONS_PER_BUNDLE,
      maxRetries: RAYDIUM_CONFIG.MAX_RATE_LIMIT_RETRIES
    }
  };
};

module.exports = {
  // Core Raydium SDK functions
  initializeRaydiumSdk,
  getRaydiumSdk,
  getRaydiumSdkWithInit,
  
  // Configuration and constants
  RAYDIUM_CONFIG,
  raydiumLaunchpadProgramId,
  getRaydiumPlatformConfig,
  
  // Shared utilities
  connection,
  jitoTipAccounts,
  delay,
  
  // Raydium-specific utilities
  logWithTimestamp,
  logErrorWithTimestamp,
  handleRaydiumRateLimit,
  categorizeRaydiumError,
  handleRaydiumError,
  
  // Provider and wallet (if needed for advanced operations)
  raydiumAnchorProvider,
  raydiumKeyPair
};
