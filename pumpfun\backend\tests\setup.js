// Test setup file for Raydium SDK V2 integration tests
// This file configures the testing environment and provides common utilities

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.SOLANA_NETWORK = 'devnet';
process.env.MONGODB_URI = 'mongodb://localhost:27017/pumpfun_test';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.ENCRYPTION_KEY = 'test_encryption_key_32_characters_long';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise during testing
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Common test utilities
global.testUtils = {
  // Generate a mock wallet for testing
  generateMockWallet: () => ({
    publicKey: 'TestPublicKey123456789',
    encryptedPrivateKey: 'encrypted_test_private_key',
    createdAt: new Date(),
  }),

  // Generate mock transaction result
  generateMockTransactionResult: (success = true) => ({
    success,
    totalWallets: 1,
    successfulTxs: success ? 1 : 0,
    failedTxs: success ? 0 : 1,
    results: success ? [{ signature: 'test_signature', walletIndex: 1 }] : [],
    errors: success ? [] : [{ error: 'Test error', walletIndex: 1 }],
    duration: 1000,
    platform: 'raydium'
  }),

  // Generate mock Raydium SDK instance
  generateMockRaydiumSdk: () => ({
    token: {
      tokenList: [],
      tokenMap: new Map(),
      mintGroup: {}
    },
    account: {
      tokenAccounts: [],
      tokenAccountRawInfos: []
    },
    api: {
      getTokenList: jest.fn().mockResolvedValue([]),
      getTokenInfo: jest.fn().mockResolvedValue([]),
      fetchPoolByMints: jest.fn().mockResolvedValue({}),
      getPoolList: jest.fn().mockResolvedValue([])
    }
  }),

  // Generate mock error with different categories
  generateMockError: (category = 'UNKNOWN_ERROR') => {
    const errors = {
      NETWORK_ERROR: new Error('Network connection failed'),
      INSUFFICIENT_FUNDS: new Error('Insufficient funds for transaction'),
      SLIPPAGE_ERROR: new Error('Price impact too high'),
      RATE_LIMITED: new Error('Too many requests'),
      TRANSACTION_ERROR: new Error('Transaction simulation failed'),
      SDK_ERROR: new Error('SDK initialization failed')
    };
    
    const error = errors[category] || new Error('Unknown error');
    error.code = category;
    return error;
  },

  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock Solana connection
  generateMockConnection: () => ({
    getBalance: jest.fn().mockResolvedValue(**********), // 1 SOL in lamports
    getTokenAccountsByOwner: jest.fn().mockResolvedValue({ value: [] }),
    getAccountInfo: jest.fn().mockResolvedValue(null),
    sendTransaction: jest.fn().mockResolvedValue('mock_signature'),
    getLatestBlockhash: jest.fn().mockResolvedValue({
      blockhash: 'mock_blockhash',
      lastValidBlockHeight: 123456
    })
  })
};

// Mock modules that are commonly used
jest.mock('@solana/web3.js', () => ({
  Connection: jest.fn().mockImplementation(() => global.testUtils.generateMockConnection()),
  Keypair: {
    generate: jest.fn().mockReturnValue({
      publicKey: { toBase58: () => 'MockPublicKey' },
      secretKey: new Uint8Array(64)
    }),
    fromSecretKey: jest.fn().mockReturnValue({
      publicKey: { toBase58: () => 'MockPublicKey' },
      secretKey: new Uint8Array(64)
    })
  },
  PublicKey: jest.fn().mockImplementation((key) => ({
    toBase58: () => key || 'MockPublicKey',
    toString: () => key || 'MockPublicKey'
  })),
  SystemProgram: {
    transfer: jest.fn().mockReturnValue({ keys: [], programId: 'SystemProgram' })
  },
  TransactionMessage: jest.fn().mockImplementation(() => ({
    compileToV0Message: jest.fn().mockReturnValue({})
  })),
  VersionedTransaction: jest.fn().mockImplementation(() => ({
    sign: jest.fn(),
    serialize: jest.fn().mockReturnValue(new Uint8Array())
  })),
  ComputeBudgetProgram: {
    setComputeUnitPrice: jest.fn().mockReturnValue({}),
    setComputeUnitLimit: jest.fn().mockReturnValue({})
  },
  LAMPORTS_PER_SOL: **********
}));

// Mock bs58 for encoding/decoding
jest.mock('bs58', () => ({
  encode: jest.fn().mockReturnValue('encoded_string'),
  decode: jest.fn().mockReturnValue(new Uint8Array(64))
}));

// Mock crypto utilities
jest.mock('../utils/crypto', () => ({
  encrypt: jest.fn().mockReturnValue('encrypted_data'),
  decrypt: jest.fn().mockReturnValue('decrypted_data')
}));

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Clean up after all tests
afterAll(() => {
  jest.restoreAllMocks();
});
