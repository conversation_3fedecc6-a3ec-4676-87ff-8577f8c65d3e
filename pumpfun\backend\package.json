{"name": "pumpbumpbot", "version": "1.0.0", "description": "pump bump bot", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:raydium": "jest --testPathPattern=raydium", "start": "node server.js"}, "keywords": ["pump", "fun", "pumpfun", "bundle", "bot"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@coral-xyz/borsh": "^0.30.1", "@raydium-io/raydium-sdk-v2": "^0.2.3-alpha", "@solana/spl-token": "^0.4.9", "@solana/web3.js": "^1.95.4", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "bs58": "^5.0.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "form-data": "^4.0.1", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.3", "multer": "^1.4.5-lts.1", "pumpdotfun-sdk": "^1.4.2"}, "devDependencies": {"nodemon": "^3.1.7", "jest": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0"}, "resolutions": {"bs58": "^5.0.0"}}