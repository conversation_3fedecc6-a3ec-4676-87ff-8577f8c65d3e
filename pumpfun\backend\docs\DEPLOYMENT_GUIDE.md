# Raydium SDK V2 Integration Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Raydium SDK V2 launchpad integration to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Configuration](#configuration)
4. [Deployment Steps](#deployment-steps)
5. [Testing Deployment](#testing-deployment)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Rollback Procedures](#rollback-procedures)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Node.js**: Version 18.x or higher
- **npm/yarn**: Latest stable version
- **MongoDB**: Version 5.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 20GB free space
- **Network**: Stable internet connection with low latency to Solana RPC

### Dependencies

```bash
# Core dependencies (already in package.json)
@raydium-io/raydium-sdk-v2: ^0.2.3-alpha
@solana/web3.js: ^1.95.4
@solana/spl-token: ^0.4.9

# Development dependencies
jest: ^29.7.0
@babel/core: ^7.23.0
@babel/preset-env: ^7.23.0
babel-jest: ^29.7.0
```

### Access Requirements

- Solana RPC endpoint access (Alchemy, QuickNode, or self-hosted)
- Jito Block Engine access (for MEV protection)
- MongoDB database access
- SSL certificates for HTTPS (production)

## Environment Setup

### Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd pumpfun/backend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run tests to verify setup
npm test

# Start development server
npm run dev
```

### Production Environment

```bash
# Install production dependencies only
npm ci --only=production

# Set production environment
export NODE_ENV=production

# Start with PM2 (recommended)
npm install -g pm2
pm2 start ecosystem.config.js
```

## Configuration

### Environment Variables Template

Create a `.env` file with the following configuration:

```bash
# Application Configuration
NODE_ENV=production
PORT=3001
API_VERSION=v1

# Database Configuration
MONGODB_URI=********************************:port/database
MONGODB_OPTIONS=retryWrites=true&w=majority

# Solana Network Configuration
SOLANA_NETWORK=mainnet-beta
SOLANA_RPC_URL=https://your-rpc-endpoint.com
SOLANA_RPC_BACKUP_URLS=https://backup1.com,https://backup2.com
SOLANA_COMMITMENT=confirmed

# Raydium SDK Configuration
RAYDIUM_PROGRAM_ID=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8
RAYDIUM_MAX_RETRIES=3
RAYDIUM_RETRY_DELAY=1000
RAYDIUM_DEFAULT_SLIPPAGE_BPS=1000

# Jito Configuration
JITO_BLOCK_ENGINE_URL=https://mainnet.block-engine.jito.wtf
JITO_TIP_ACCOUNTS=["Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"]
JITO_MAX_RETRIES=3

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-here
ENCRYPTION_KEY=your-32-character-encryption-key
CORS_ORIGIN=https://your-frontend-domain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/pumpfun/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Performance Configuration
MAX_CONCURRENT_OPERATIONS=10
TRANSACTION_TIMEOUT_MS=30000
SDK_CACHE_TTL_MS=3600000

# Monitoring Configuration
HEALTH_CHECK_INTERVAL_MS=30000
METRICS_ENABLED=true
METRICS_PORT=9090
```

### PM2 Ecosystem Configuration

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'pumpfun-backend',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/pumpfun/err.log',
    out_file: '/var/log/pumpfun/out.log',
    log_file: '/var/log/pumpfun/combined.log',
    time: true,
    max_memory_restart: '2G',
    node_args: '--max-old-space-size=4096',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'coverage'],
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

### Nginx Configuration

Create `/etc/nginx/sites-available/pumpfun-backend`:

```nginx
server {
    listen 80;
    server_name api.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    location /health {
        proxy_pass http://localhost:3001/health;
        access_log off;
    }
}
```

## Deployment Steps

### Step 1: Pre-deployment Checklist

```bash
# 1. Verify all tests pass
npm test

# 2. Check code quality
npm run lint

# 3. Build production assets (if applicable)
npm run build

# 4. Verify environment configuration
node -e "console.log('Environment check:', process.env.NODE_ENV)"

# 5. Test database connectivity
node -e "require('./utils/dbConnect')().then(() => console.log('DB OK'))"
```

### Step 2: Database Migration

```bash
# Backup existing database
mongodump --uri="$MONGODB_URI" --out=backup-$(date +%Y%m%d-%H%M%S)

# Run any necessary migrations
node scripts/migrate.js

# Verify data integrity
node scripts/verify-data.js
```

### Step 3: Application Deployment

```bash
# Stop existing application
pm2 stop pumpfun-backend

# Update application code
git pull origin main

# Install/update dependencies
npm ci --only=production

# Start application
pm2 start ecosystem.config.js

# Verify deployment
pm2 status
pm2 logs pumpfun-backend --lines 50
```

### Step 4: Frontend Deployment

```bash
# Navigate to frontend directory
cd ../frontend

# Install dependencies
npm ci

# Build production assets
npm run build

# Deploy to web server
rsync -avz build/ user@server:/var/www/pumpfun/

# Update nginx configuration if needed
sudo nginx -t
sudo systemctl reload nginx
```

## Testing Deployment

### Health Checks

```bash
# Basic health check
curl https://api.your-domain.com/health

# Raydium-specific health check
curl https://api.your-domain.com/api/health/raydium

# Database connectivity
curl https://api.your-domain.com/api/health/database
```

### Functional Testing

```bash
# Test wallet operations
curl -X POST https://api.your-domain.com/api/wallets \
  -H "Content-Type: application/json" \
  -d '{"count": 1}'

# Test platform routing
curl -X POST https://api.your-domain.com/api/buy \
  -H "Content-Type: application/json" \
  -d '{
    "selectedWallets": ["test"],
    "tokenMint": "test",
    "amounts": {"test": "0.001"},
    "jitoTip": "0.0001",
    "isPump": false
  }'
```

### Performance Testing

```bash
# Load testing with Apache Bench
ab -n 100 -c 10 https://api.your-domain.com/health

# Monitor resource usage
htop
iostat -x 1
```

## Monitoring and Maintenance

### Log Monitoring

```bash
# Monitor application logs
pm2 logs pumpfun-backend --lines 100 --timestamp

# Monitor error logs
tail -f /var/log/pumpfun/err.log

# Monitor nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### Performance Monitoring

```bash
# PM2 monitoring
pm2 monit

# System resource monitoring
htop
iotop
nethogs
```

### Database Monitoring

```bash
# MongoDB status
mongo --eval "db.runCommand({serverStatus: 1})"

# Connection monitoring
mongo --eval "db.runCommand({currentOp: 1})"
```

### Automated Monitoring Setup

Create monitoring scripts:

```bash
#!/bin/bash
# health-check.sh
HEALTH_URL="https://api.your-domain.com/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -ne 200 ]; then
    echo "Health check failed: $RESPONSE"
    # Send alert (email, Slack, etc.)
    exit 1
fi

echo "Health check passed"
```

Add to crontab:
```bash
# Check health every 5 minutes
*/5 * * * * /path/to/health-check.sh
```

## Rollback Procedures

### Quick Rollback

```bash
# Stop current version
pm2 stop pumpfun-backend

# Revert to previous version
git checkout HEAD~1

# Reinstall dependencies
npm ci --only=production

# Start previous version
pm2 start ecosystem.config.js

# Verify rollback
curl https://api.your-domain.com/health
```

### Database Rollback

```bash
# Stop application
pm2 stop pumpfun-backend

# Restore database from backup
mongorestore --uri="$MONGODB_URI" --drop backup-folder/

# Start application
pm2 start ecosystem.config.js
```

## Troubleshooting

### Common Issues

1. **SDK Initialization Failures**
   ```bash
   # Check RPC connectivity
   curl -X POST $SOLANA_RPC_URL \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}'
   ```

2. **Memory Issues**
   ```bash
   # Monitor memory usage
   pm2 show pumpfun-backend
   
   # Restart if memory usage is high
   pm2 restart pumpfun-backend
   ```

3. **Database Connection Issues**
   ```bash
   # Test MongoDB connection
   mongo $MONGODB_URI --eval "db.runCommand({ping: 1})"
   ```

### Emergency Procedures

1. **Service Down**
   ```bash
   pm2 restart pumpfun-backend
   sudo systemctl restart nginx
   ```

2. **High Load**
   ```bash
   # Scale up instances
   pm2 scale pumpfun-backend +2
   ```

3. **Database Issues**
   ```bash
   # Switch to backup database
   export MONGODB_URI=$MONGODB_BACKUP_URI
   pm2 restart pumpfun-backend
   ```

For additional support, refer to the API documentation and contact the development team.
