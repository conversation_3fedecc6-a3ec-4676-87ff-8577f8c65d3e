# Raydium SDK V2 Launchpad Integration API Documentation

## Overview

This document provides comprehensive documentation for the Raydium SDK V2 launchpad integration, which enables dual-platform trading support alongside the existing PumpFun functionality.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [API Endpoints](#api-endpoints)
3. [Configuration](#configuration)
4. [Error Handling](#error-handling)
5. [Testing](#testing)
6. [Performance Considerations](#performance-considerations)
7. [Troubleshooting](#troubleshooting)

## Architecture Overview

The Raydium integration follows a modular architecture that seamlessly integrates with the existing PumpFun infrastructure:

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │   Backend API   │
│                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Platform    │ │    │ │ Route       │ │
│ │ Selection   │ │◄──►│ │ Handler     │ │
│ │ (PF/RDM)    │ │    │ │ (isPump)    │ │
│ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Platform Router │
                       │                 │
                       │ ┌─────┐ ┌─────┐ │
                       │ │ PF  │ │ RDM │ │
                       │ │ Ops │ │ Ops │ │
                       │ └─────┘ └─────┘ │
                       └─────────────────┘
```

### Core Components

- **raydiumConfig.js**: SDK initialization and configuration management
- **raydiumBuyOperations.js**: Buy operation implementations
- **raydiumSellOperations.js**: Sell operation implementations
- **buyOperations.js/sellOperations.js**: Platform routing logic

## API Endpoints

### Buy Operations

#### POST /api/buy

Executes buy operations on the selected platform (PumpFun or Raydium).

**Request Body:**
```json
{
  "selectedWallets": ["wallet1", "wallet2"],
  "tokenMint": "TokenMintAddress123",
  "amounts": {
    "wallet1": "0.1",
    "wallet2": "0.2"
  },
  "jitoTip": "0.0001",
  "isPump": false,
  "raydiumParams": {
    "slippageBps": 1000
  }
}
```

**Parameters:**
- `selectedWallets` (array): List of wallet public keys
- `tokenMint` (string): Token mint address
- `amounts` (object): SOL amounts per wallet
- `jitoTip` (string): Jito tip amount in SOL
- `isPump` (boolean): Platform selection (true = PumpFun, false = Raydium)
- `raydiumParams` (object, optional): Raydium-specific parameters
  - `slippageBps` (number): Slippage tolerance in basis points (default: 1000)

**Response:**
```json
{
  "success": true,
  "totalWallets": 2,
  "successfulTxs": 2,
  "failedTxs": 0,
  "results": [
    {
      "signature": "transaction_signature_1",
      "walletIndex": 1,
      "walletPublicKey": "wallet1",
      "amount": "0.1",
      "success": true,
      "duration": 1500
    }
  ],
  "errors": [],
  "duration": 3000,
  "platform": "raydium"
}
```

### Sell Operations

#### POST /api/sell

Executes sell operations on the selected platform.

**Request Body:**
```json
{
  "selectedWallets": ["wallet1", "wallet2"],
  "tokenMint": "TokenMintAddress123",
  "sellPercentage": 50,
  "jitoTip": "0.0001",
  "isPump": false,
  "raydiumParams": {
    "slippageBps": 1000
  }
}
```

**Parameters:**
- `selectedWallets` (array): List of wallet public keys
- `tokenMint` (string): Token mint address
- `sellPercentage` (number): Percentage of tokens to sell (1-100)
- `jitoTip` (string): Jito tip amount in SOL
- `isPump` (boolean): Platform selection
- `raydiumParams` (object, optional): Raydium-specific parameters

**Response:**
```json
{
  "success": true,
  "totalWallets": 2,
  "successfulTxs": 2,
  "failedTxs": 0,
  "results": [
    {
      "signature": "transaction_signature_1",
      "walletIndex": 1,
      "walletPublicKey": "wallet1",
      "percentage": 50,
      "success": true,
      "duration": 1500
    }
  ],
  "errors": [],
  "duration": 3000,
  "platform": "raydium"
}
```

## Configuration

### Environment Variables

```bash
# Solana Network Configuration
SOLANA_NETWORK=devnet  # or mainnet-beta
SOLANA_RPC_URL=https://api.devnet.solana.com

# Raydium Configuration
RAYDIUM_PROGRAM_ID=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8
RAYDIUM_MAX_RETRIES=3
RAYDIUM_RETRY_DELAY=1000

# Jito Configuration
JITO_TIP_ACCOUNTS=["Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"]
JITO_BLOCK_ENGINE_URL=https://mainnet.block-engine.jito.wtf

# Database
MONGODB_URI=mongodb://localhost:27017/pumpfun
```

### Raydium Configuration Constants

```javascript
const RAYDIUM_CONFIG = {
  MAX_TRANSACTIONS_PER_BUNDLE: 3,
  RATE_LIMIT_RETRY_DELAY: 1000,
  MAX_RATE_LIMIT_RETRIES: 2,
  PRIORITY_FEE_UNIT_LIMIT: 100000,
  PRIORITY_FEE_UNIT_PRICE: 25000,
  DEFAULT_SLIPPAGE_BPS: 1000  // 10%
};
```

## Error Handling

### Error Categories

The system categorizes errors into specific types for better handling:

1. **NETWORK_ERROR**: Connection issues, RPC failures
2. **INSUFFICIENT_FUNDS**: Wallet balance too low
3. **SLIPPAGE_ERROR**: Price impact too high
4. **RATE_LIMITED**: API rate limiting
5. **TRANSACTION_ERROR**: Transaction simulation/execution failures
6. **SDK_ERROR**: Raydium SDK initialization or method errors
7. **UNKNOWN_ERROR**: Uncategorized errors

### Error Response Format

```json
{
  "success": false,
  "totalWallets": 2,
  "successfulTxs": 0,
  "failedTxs": 2,
  "results": [],
  "errors": [
    {
      "walletIndex": 1,
      "walletPublicKey": "wallet1",
      "error": "Insufficient funds for transaction",
      "errorCategory": "INSUFFICIENT_FUNDS",
      "errorSeverity": "MEDIUM",
      "retryable": false,
      "userMessage": "Insufficient funds for this transaction.",
      "amount": "0.1",
      "duration": 500,
      "success": false
    }
  ],
  "duration": 1000,
  "platform": "raydium"
}
```

### Error Severity Levels

- **LOW**: Minor issues, operation can continue
- **MEDIUM**: Significant issues, may require user action
- **HIGH**: Critical issues, operation should be halted

### Retryable Errors

The system automatically identifies which errors are retryable:
- Network errors: Usually retryable
- Rate limiting: Retryable with delay
- Slippage errors: Retryable with adjusted parameters
- Insufficient funds: Not retryable
- Invalid parameters: Not retryable

## Testing

### Running Tests

```bash
# Install test dependencies
npm install

# Run all tests
npm test

# Run specific test suites
npm run test:raydium
node tests/runTests.js run unit
node tests/runTests.js coverage integration

# Run tests in watch mode
npm run test:watch
node tests/runTests.js watch performance
```

### Test Suites

1. **Unit Tests**: Test individual functions and modules
2. **Integration Tests**: Test interaction with existing systems
3. **Error Scenario Tests**: Test error handling and edge cases
4. **Performance Tests**: Benchmark and optimization tests

### Mock Implementation

The current implementation includes placeholder functionality that:
- Validates all input parameters
- Implements proper error handling
- Maintains consistent response formats
- Provides comprehensive logging
- Supports all configuration options

## Performance Considerations

### Benchmarks

- Single wallet operation: < 2 seconds
- 10 wallet batch: < 8 seconds
- 50 wallet batch: < 30 seconds
- SDK initialization: < 2 seconds (cached after first call)

### Optimization Features

1. **SDK Caching**: Raydium SDK instance is cached after initialization
2. **Concurrent Processing**: Wallet operations are processed in parallel
3. **Bundle Optimization**: Transactions are bundled for efficiency
4. **Memory Management**: Proper cleanup to prevent memory leaks
5. **Error Fast-Fail**: Invalid inputs fail quickly without resource waste

### Monitoring

The system provides detailed logging for monitoring:
- Operation timing and performance metrics
- Error categorization and frequency
- Resource utilization tracking
- Transaction success/failure rates

## Troubleshooting

### Common Issues

1. **SDK Initialization Failures**
   - Check network connectivity
   - Verify RPC endpoint availability
   - Ensure proper environment configuration

2. **Transaction Failures**
   - Verify wallet balances
   - Check slippage tolerance settings
   - Confirm token mint address validity

3. **Rate Limiting**
   - Implement proper retry logic
   - Use multiple RPC endpoints
   - Adjust request frequency

4. **Performance Issues**
   - Monitor memory usage
   - Check network latency
   - Optimize batch sizes

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
DEBUG=raydium:*
```

### Health Checks

The system provides health check endpoints:
- `/api/health`: General system health
- `/api/health/raydium`: Raydium SDK status
- `/api/health/database`: Database connectivity

For additional support, refer to the test files and implementation examples in the codebase.
