// Integration tests for Raydium operations with existing wallet management
const { buyRaydium } = require('../utils/raydiumBuyOperations');
const { sellRaydium } = require('../utils/raydiumSellOperations');
const { buy, sell } = require('../utils/buyOperations');
const { encrypt, decrypt } = require('../utils/crypto');

// Mock dependencies
jest.mock('../utils/raydiumConfig');
jest.mock('../utils/transactionUtils');
jest.mock('../utils/config');

const raydiumConfig = require('../utils/raydiumConfig');
const config = require('../utils/config');

describe('Wallet Management Integration Tests', () => {
  const mockWalletData = {
    publicKey: 'TestWallet123456789',
    encryptedPrivateKey: 'encrypted_test_key',
    createdAt: new Date()
  };

  const mockAmounts = {
    'TestWallet123456789': '0.1'
  };

  const mockToken = 'TokenMintAddress123';
  const mockJitoTip = '0.0001';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock Raydium SDK
    raydiumConfig.getRaydiumSdkWithInit.mockResolvedValue(
      global.testUtils.generateMockRaydiumSdk()
    );
    
    // Mock logging functions
    raydiumConfig.logWithTimestamp.mockImplementation(() => {});
    raydiumConfig.logErrorWithTimestamp.mockImplementation(() => {});
    raydiumConfig.handleRaydiumError.mockReturnValue({
      category: 'UNKNOWN_ERROR',
      severity: 'HIGH',
      retryable: false,
      userMessage: 'An unexpected error occurred'
    });

    // Mock config
    config.getTokenDecimals.mockResolvedValue(9);
    config.connection = global.testUtils.generateMockConnection();
  });

  describe('Wallet encryption/decryption integration', () => {
    test('should work with encrypted wallet data for Raydium buy operations', async () => {
      const wallets = [mockWalletData];
      
      const result = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);

      // Should process the wallet even though it fails due to placeholder
      expect(result.totalWallets).toBe(1);
      expect(result.platform).toBe('raydium');
      
      // Verify that decrypt was called for the wallet
      expect(decrypt).toHaveBeenCalledWith(mockWalletData.encryptedPrivateKey);
    });

    test('should work with encrypted wallet data for Raydium sell operations', async () => {
      const wallets = [mockWalletData];
      
      const result = await sellRaydium(wallets, 50, mockToken, mockJitoTip, false);

      // Should process the wallet even though it fails due to placeholder
      expect(result.totalWallets).toBe(1);
      expect(result.platform).toBe('raydium');
      
      // Verify that decrypt was called for the wallet
      expect(decrypt).toHaveBeenCalledWith(mockWalletData.encryptedPrivateKey);
    });

    test('should handle multiple wallets with different encryption keys', async () => {
      const multipleWallets = [
        { ...mockWalletData, encryptedPrivateKey: 'encrypted_key_1' },
        { ...mockWalletData, encryptedPrivateKey: 'encrypted_key_2' },
        { ...mockWalletData, encryptedPrivateKey: 'encrypted_key_3' }
      ];

      const result = await buyRaydium(multipleWallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(3);
      expect(decrypt).toHaveBeenCalledTimes(3);
      expect(decrypt).toHaveBeenCalledWith('encrypted_key_1');
      expect(decrypt).toHaveBeenCalledWith('encrypted_key_2');
      expect(decrypt).toHaveBeenCalledWith('encrypted_key_3');
    });
  });

  describe('Platform routing integration', () => {
    test('should route to PumpFun when isPump is true', async () => {
      // Mock PumpFun operations
      const mockPumpFunResult = global.testUtils.generateMockTransactionResult(true);
      
      // This would normally call the actual PumpFun implementation
      // For testing, we'll verify the routing logic works
      const wallets = [mockWalletData];
      
      // Test the routing in the main buy function
      const result = await buy(wallets, mockAmounts, mockToken, mockJitoTip, true);
      
      // Should route to PumpFun (this will fail in test environment but shows routing works)
      expect(result).toBeDefined();
    });

    test('should route to Raydium when isPump is false', async () => {
      const wallets = [mockWalletData];
      
      const result = await buy(wallets, mockAmounts, mockToken, mockJitoTip, false);
      
      // Should route to Raydium
      expect(result.platform).toBe('raydium');
    });

    test('should route sell operations correctly', async () => {
      const wallets = [mockWalletData];
      
      // Test Raydium routing
      const raydiumResult = await sell(wallets, 50, mockToken, mockJitoTip, false);
      expect(raydiumResult.platform).toBe('raydium');
    });
  });

  describe('Database operations integration', () => {
    test('should work with wallet data from database format', async () => {
      const dbWalletFormat = {
        _id: 'mongodb_object_id',
        publicKey: 'TestWallet123456789',
        encryptedPrivateKey: 'encrypted_test_key',
        createdAt: new Date(),
        __v: 0
      };

      const wallets = [dbWalletFormat];
      
      const result = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(1);
      expect(decrypt).toHaveBeenCalledWith(dbWalletFormat.encryptedPrivateKey);
    });

    test('should handle missing wallet properties gracefully', async () => {
      const incompleteWallet = {
        publicKey: 'TestWallet123456789'
        // Missing encryptedPrivateKey
      };

      const wallets = [incompleteWallet];
      
      const result = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(1);
    });
  });

  describe('Multi-wallet operations integration', () => {
    test('should handle concurrent operations on multiple wallets', async () => {
      const multipleWallets = Array(10).fill().map((_, i) => ({
        publicKey: `TestWallet${i}`,
        encryptedPrivateKey: `encrypted_key_${i}`,
        createdAt: new Date()
      }));

      const multipleAmounts = {};
      multipleWallets.forEach(wallet => {
        multipleAmounts[wallet.publicKey] = '0.1';
      });

      const result = await buyRaydium(multipleWallets, multipleAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(10);
      expect(result.failedTxs).toBe(10); // All fail due to placeholder
      expect(decrypt).toHaveBeenCalledTimes(10);
    });

    test('should handle partial wallet processing failures', async () => {
      const mixedWallets = [
        { publicKey: 'ValidWallet1', encryptedPrivateKey: 'valid_key_1' },
        { publicKey: 'ValidWallet2', encryptedPrivateKey: 'valid_key_2' },
        { publicKey: 'InvalidWallet' } // Missing encryptedPrivateKey
      ];

      const mixedAmounts = {
        'ValidWallet1': '0.1',
        'ValidWallet2': '0.2'
        // Missing amount for InvalidWallet
      };

      const result = await buyRaydium(mixedWallets, mixedAmounts, mockToken, mockJitoTip, false);

      expect(result.totalWallets).toBe(3);
      expect(result.failedTxs).toBe(3); // All fail for different reasons
      expect(result.errors).toHaveLength(3);
    });
  });

  describe('Error handling integration', () => {
    test('should maintain error consistency across platforms', async () => {
      const wallets = [mockWalletData];
      
      // Test Raydium error handling
      const raydiumResult = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);
      
      expect(raydiumResult.errors[0]).toEqual(
        expect.objectContaining({
          walletIndex: expect.any(Number),
          walletPublicKey: expect.any(String),
          error: expect.any(String),
          errorCategory: expect.any(String),
          errorSeverity: expect.any(String),
          retryable: expect.any(Boolean),
          userMessage: expect.any(String),
          duration: expect.any(Number),
          success: false
        })
      );
    });

    test('should handle wallet decryption failures', async () => {
      // Mock decrypt to throw an error
      decrypt.mockImplementation(() => {
        throw new Error('Decryption failed');
      });

      const wallets = [mockWalletData];
      
      const result = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);

      expect(result.success).toBe(false);
      expect(result.failedTxs).toBe(1);
    });
  });

  describe('Performance integration', () => {
    test('should complete operations within reasonable time limits', async () => {
      const startTime = Date.now();
      const wallets = [mockWalletData];
      
      const result = await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);
      const endTime = Date.now();
      
      const operationTime = endTime - startTime;
      
      // Should complete within 5 seconds for single wallet
      expect(operationTime).toBeLessThan(5000);
      expect(result.duration).toBeGreaterThan(0);
      expect(result.duration).toBeLessThan(operationTime + 100);
    });

    test('should scale reasonably with multiple wallets', async () => {
      const smallBatch = Array(2).fill().map((_, i) => ({
        publicKey: `TestWallet${i}`,
        encryptedPrivateKey: `encrypted_key_${i}`
      }));

      const largeBatch = Array(5).fill().map((_, i) => ({
        publicKey: `TestWallet${i}`,
        encryptedPrivateKey: `encrypted_key_${i}`
      }));

      const smallAmounts = {};
      const largeAmounts = {};
      
      smallBatch.forEach(w => smallAmounts[w.publicKey] = '0.1');
      largeBatch.forEach(w => largeAmounts[w.publicKey] = '0.1');

      const smallResult = await buyRaydium(smallBatch, smallAmounts, mockToken, mockJitoTip, false);
      const largeResult = await buyRaydium(largeBatch, largeAmounts, mockToken, mockJitoTip, false);

      // Larger batch should not take exponentially longer
      expect(largeResult.duration).toBeLessThan(smallResult.duration * 5);
    });
  });

  describe('Configuration integration', () => {
    test('should use shared configuration values', async () => {
      const wallets = [mockWalletData];
      
      await buyRaydium(wallets, mockAmounts, mockToken, mockJitoTip, false);

      // Should use shared connection and configuration
      expect(config.getTokenDecimals).toHaveBeenCalledWith(mockToken);
      expect(raydiumConfig.getRaydiumSdkWithInit).toHaveBeenCalled();
    });

    test('should handle configuration failures gracefully', async () => {
      config.getTokenDecimals.mockRejectedValue(new Error('Config error'));
      
      const wallets = [mockWalletData];
      
      await expect(sellRaydium(wallets, 50, mockToken, mockJitoTip, false))
        .rejects.toThrow();
    });
  });
});
