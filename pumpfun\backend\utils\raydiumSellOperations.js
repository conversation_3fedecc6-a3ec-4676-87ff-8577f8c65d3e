const {
  VersionedTransaction,
  TransactionMessage,
  SystemProgram,
  <PERSON>Key,
  Keypair,
  LAMPORTS_PER_SOL,
  ComputeBudgetProgram
} = require("@solana/web3.js");
const bs58 = require("bs58");
const { decrypt } = require("./crypto");
const {
  connection,
  jitoTipAccounts,
  delay,
  getTokenDecimals
} = require("./config");
const {
  getRaydiumSdkWithInit,
  logWithTimestamp,
  logErrorWithTimestamp,
  handleRaydiumError,
  categorizeRaydiumError,
  RAYDIUM_CONFIG
} = require("./raydiumConfig");
const { submitBundle, getLatestBlockhashWithRetry, submitTransactionToJito } = require("./transactionUtils");

// Priority fee constants for Raydium operations
const PRIORITY_FEE_UNIT_LIMIT = RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_LIMIT;
const PRIORITY_FEE_UNIT_PRICE = RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_PRICE;

/**
 * Sell tokens on Raydium launchpad for multiple wallets
 * Note: This implementation mirrors the PumpFun sellOperations.js structure
 * but uses Raydium SDK V2 launchpad functionality
 *
 * @param {Array} wallets_ - Array of wallet objects with encrypted private keys
 * @param {number} sellPercentage - Percentage of tokens to sell (same for all wallets)
 * @param {string} token - Token mint address
 * @param {string} jitoTip - Jito tip amount in SOL
 * @param {boolean} isPump - Platform identifier (false for Raydium)
 * @returns {Promise<Object>} - Result object with transaction details
 */
const sellRaydium = async (wallets_, sellPercentage, token, jitoTip, isPump = false) => {
  const startTime = Date.now();
  const walletCount = wallets_.length;
  logWithTimestamp(`Starting Raydium launchpad sell operation for ${walletCount} wallet(s) for token ${token}`);

  try {
    // Initialize Raydium SDK if not already done
    const raydiumSdk = await getRaydiumSdkWithInit();
    logWithTimestamp(`Raydium SDK initialized successfully`);

    // Get token decimals for calculations
    const tokenDecimals = await getTokenDecimals(token);
    logWithTimestamp(`Token decimals: ${tokenDecimals}`);

    // Track which wallets are being processed
    const walletPublicKeys = wallets_.map(w => {
      const wallet = Keypair.fromSecretKey(bs58.decode(decrypt(w.encryptedPrivateKey)));
      return wallet.publicKey.toBase58();
    });
    logWithTimestamp(`Processing wallets:`, walletPublicKeys);

    const sellPromises = wallets_.map(async (walletData, walletIndex) => {
      const walletStartTime = Date.now();
      const wallet = Keypair.fromSecretKey(
        bs58.decode(decrypt(walletData.encryptedPrivateKey))
      );
      const walletPublicKey = wallet.publicKey.toBase58();

      logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Processing Raydium launchpad sell for wallet ${walletPublicKey}`);

      logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Sell percentage: ${sellPercentage}%`);

      try {
        // Get token balance for this wallet
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Getting token balance`);
        
        // Get token accounts for the wallet
        const tokenAccounts = await connection.getTokenAccountsByOwner(
          wallet.publicKey,
          { mint: new PublicKey(token) }
        );

        if (tokenAccounts.value.length === 0) {
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] No token account found for token ${token}`);
          return {
            walletIndex: walletIndex + 1,
            walletPublicKey,
            error: `No token account found for token ${token}`,
            percentage: sellPercentage,
            duration: Date.now() - walletStartTime,
            success: false
          };
        }

        // Parse token account data to get balance
        const tokenAccountInfo = await connection.getAccountInfo(tokenAccounts.value[0].pubkey);
        if (!tokenAccountInfo) {
          throw new Error(`Failed to get token account info`);
        }

        // Extract balance from token account data (simplified parsing)
        // In a real implementation, you'd use proper SPL token parsing
        const tokenBalance = tokenAccountInfo.lamports; // This is a placeholder
        
        if (tokenBalance === 0) {
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Token balance is 0`);
          return {
            walletIndex: walletIndex + 1,
            walletPublicKey,
            error: `Token balance is 0`,
            percentage: sellPercentage,
            duration: Date.now() - walletStartTime,
            success: false
          };
        }

        // Calculate amount to sell based on percentage
        const sellAmount = Math.floor((tokenBalance * Number(sellPercentage)) / 100);
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Token balance: ${tokenBalance}, Sell amount: ${sellAmount}`);

        // Note: The exact Raydium launchpad API methods need to be determined
        // Based on the LaunchLab documentation, there should be methods like:
        // - raydiumSdk.launchpad.sell() or similar
        // - Methods to get launchpad pool information
        // - Bonding curve price calculations for selling
        
        // For now, I'll implement a placeholder structure that follows the
        // Raydium SDK V2 transaction pattern: { execute, transaction, builder, extInfo }
        
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Getting Raydium launchpad pool information for sell`);
        
        // TODO: Replace with actual Raydium launchpad API calls
        // This is a placeholder implementation that needs to be updated
        // with the correct Raydium SDK V2 launchpad methods
        
        // Example structure based on Raydium SDK V2 patterns:
        // const launchpadPool = await raydiumSdk.launchpad.getPoolInfo(new PublicKey(token));
        // const sellResult = await raydiumSdk.launchpad.sell({
        //   poolId: launchpadPool.id,
        //   amount: sellAmount,
        //   slippage: RAYDIUM_CONFIG.DEFAULT_SLIPPAGE_BPS
        // });
        
        // For now, throw an error indicating this needs implementation
        throw new Error(`Raydium launchpad sell functionality requires specific API implementation. Token: ${token}, Percentage: ${sellPercentage}%`);
        
        // The following code structure shows how it would work once the API is implemented:
        /*
        const { execute, transaction, builder, extInfo } = sellResult;
        
        // Add priority fee instructions
        const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
          microLamports: PRIORITY_FEE_UNIT_PRICE
        });
        
        const computeUnitLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
          units: PRIORITY_FEE_UNIT_LIMIT
        });
        
        // Add Jito tip if specified
        const instructions = [computeUnitLimitInstruction, priorityFeeInstruction];
        
        if (Number(jitoTip) > 0) {
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Adding Jito tip of ${jitoTip} SOL`);
          const tipAccountNumber = Math.floor(8 * Math.random());
          const tipInstruction = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: jitoTipAccounts[tipAccountNumber],
            lamports: Math.floor(Number(jitoTip) * LAMPORTS_PER_SOL),
          });
          instructions.unshift(tipInstruction);
        }
        
        // Add the Raydium sell instructions
        instructions.push(...builder.allInstructions);
        
        // Create and execute transaction
        let tries = 0;
        const maxRetries = 3;
        
        while (tries < maxRetries) {
          try {
            const latestBlockhash = await getLatestBlockhashWithRetry(connection);
            
            const sellMessage = new TransactionMessage({
              payerKey: wallet.publicKey,
              recentBlockhash: latestBlockhash.blockhash,
              instructions: instructions,
            }).compileToV0Message();
            
            const sellTransaction = new VersionedTransaction(sellMessage);
            sellTransaction.sign([wallet]);
            
            let signature;
            
            if (Number(jitoTip) > 0) {
              signature = await submitTransactionToJito(sellTransaction, `RaydiumSell-Wallet-${walletIndex + 1}`);
            } else {
              signature = await connection.sendTransaction(sellTransaction, {
                skipPreflight: true,
                maxRetries: 2,
              });
            }
            
            logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Transaction submitted successfully: ${signature}`);
            
            const walletDuration = Date.now() - walletStartTime;
            return {
              walletIndex: walletIndex + 1,
              walletPublicKey,
              signature,
              percentage,
              sellAmount,
              duration: walletDuration,
              success: true
            };
            
          } catch (error) {
            tries++;
            logErrorWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Attempt ${tries} failed`, error);
            
            if (tries >= maxRetries) {
              throw error;
            }
            
            await delay(500);
          }
        }
        */
        
      } catch (error) {
        const walletDuration = Date.now() - walletStartTime;

        // Use enhanced error handling
        const errorCategory = handleRaydiumError(
          error,
          'Raydium sell operation failed',
          walletIndex + 1
        );

        return {
          walletIndex: walletIndex + 1,
          walletPublicKey,
          error: error.message,
          errorCategory: errorCategory.category,
          errorSeverity: errorCategory.severity,
          retryable: errorCategory.retryable,
          userMessage: errorCategory.userMessage,
          percentage: sellPercentage,
          duration: walletDuration,
          success: false
        };
      }
    });

    // Wait for all wallet operations to complete
    logWithTimestamp(`Waiting for all ${walletCount} Raydium sell operations to complete...`);
    const results = await Promise.allSettled(sellPromises);
    
    // Process results
    const successfulTxs = [];
    const failedTxs = [];
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        successfulTxs.push(result.value);
      } else {
        const errorInfo = result.status === 'rejected' 
          ? { walletIndex: index + 1, error: result.reason.message }
          : result.value;
        failedTxs.push(errorInfo);
      }
    });
    
    const totalDuration = Date.now() - startTime;
    
    logWithTimestamp(`Raydium sell operation completed in ${totalDuration}ms`);
    logWithTimestamp(`Successful transactions: ${successfulTxs.length}`);
    logWithTimestamp(`Failed transactions: ${failedTxs.length}`);
    
    if (failedTxs.length > 0) {
      logErrorWithTimestamp(`Failed transactions:`, failedTxs);
    }
    
    return {
      success: failedTxs.length === 0,
      totalWallets: walletCount,
      successfulTxs: successfulTxs.length,
      failedTxs: failedTxs.length,
      results: successfulTxs,
      errors: failedTxs,
      duration: totalDuration,
      platform: 'raydium'
    };
    
  } catch (err) {
    const totalDuration = Date.now() - startTime;

    // Use enhanced error handling for the overall operation
    const errorCategory = handleRaydiumError(err, 'Overall Raydium sell operation failed');

    logErrorWithTimestamp(
      `Raydium sell operation failed after ${totalDuration}ms - ${errorCategory.category}`,
      {
        originalError: err.message,
        category: errorCategory.category,
        severity: errorCategory.severity,
        retryable: errorCategory.retryable,
        duration: totalDuration,
        walletCount
      }
    );

    // Re-throw with enhanced error information
    const enhancedError = new Error(errorCategory.userMessage);
    enhancedError.originalError = err;
    enhancedError.category = errorCategory.category;
    enhancedError.severity = errorCategory.severity;
    enhancedError.retryable = errorCategory.retryable;
    enhancedError.duration = totalDuration;

    throw enhancedError;
  }
};

module.exports = {
  sellRaydium
};
