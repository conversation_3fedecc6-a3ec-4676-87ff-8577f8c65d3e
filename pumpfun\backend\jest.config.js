// Jest configuration for Raydium SDK V2 integration tests
module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Test file patterns
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js'
  ],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'utils/raydium*.js',
    'utils/buyOperations.js',
    'utils/sellOperations.js',
    'controllers/walletController.js',
    '!utils/config.js', // Exclude config from coverage
    '!**/node_modules/**',
    '!**/tests/**'
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './utils/raydiumConfig.js': {
      branches: 80,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },

  // Module path mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  },

  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,

  // Verbose output
  verbose: true,

  // Test timeout
  testTimeout: 30000,

  // Error handling
  errorOnDeprecated: true,

  // Module directories
  moduleDirectories: ['node_modules', '<rootDir>'],

  // Files to ignore
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/'
  ],

  // Global variables
  globals: {
    'process.env.NODE_ENV': 'test'
  }
};
