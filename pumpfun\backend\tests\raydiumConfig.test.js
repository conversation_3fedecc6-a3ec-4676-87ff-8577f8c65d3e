// Unit tests for Raydium configuration module
const {
  initializeRaydiumSdk,
  getRaydiumSdk,
  getRaydiumSdkWithInit,
  RAYDIUM_CONFIG,
  getRaydiumPlatformConfig,
  logWithTimestamp,
  logErrorWithTimestamp,
  categorizeRaydiumError,
  handleRaydiumError,
  handleRaydiumRateLimit
} = require('../utils/raydiumConfig');

// Mock the Raydium SDK
jest.mock('@raydium-io/raydium-sdk-v2', () => ({
  Raydium: {
    load: jest.fn()
  }
}));

const { Raydium } = require('@raydium-io/raydium-sdk-v2');

describe('Raydium Configuration Module', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the module state
    jest.resetModules();
  });

  describe('RAYDIUM_CONFIG constants', () => {
    test('should have correct default configuration values', () => {
      expect(RAYDIUM_CONFIG).toEqual({
        MAX_TRANSACTIONS_PER_BUNDLE: 3,
        RATE_LIMIT_RETRY_DELAY: 1000,
        MAX_RATE_LIMIT_RETRIES: 2,
        PRIORITY_FEE_UNIT_LIMIT: 100000,
        PRIORITY_FEE_UNIT_PRICE: 25000,
        DEFAULT_SLIPPAGE_BPS: 1000
      });
    });
  });

  describe('initializeRaydiumSdk', () => {
    test('should initialize Raydium SDK successfully', async () => {
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);

      const result = await initializeRaydiumSdk();

      expect(Raydium.load).toHaveBeenCalledWith({
        connection: expect.any(Object),
        owner: expect.any(Object),
        disableLoadToken: false,
        cluster: 'devnet'
      });
      expect(result).toBe(mockSdk);
    });

    test('should return existing SDK instance if already initialized', async () => {
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);

      // First initialization
      const result1 = await initializeRaydiumSdk();
      // Second call should return the same instance
      const result2 = await initializeRaydiumSdk();

      expect(Raydium.load).toHaveBeenCalledTimes(1);
      expect(result1).toBe(result2);
    });

    test('should handle initialization errors', async () => {
      const error = new Error('SDK initialization failed');
      Raydium.load.mockRejectedValue(error);

      await expect(initializeRaydiumSdk()).rejects.toThrow('SDK initialization failed');
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to initialize Raydium SDK V2:'),
        error
      );
    });

    test('should reset state on initialization failure', async () => {
      const error = new Error('SDK initialization failed');
      Raydium.load.mockRejectedValue(error);

      await expect(initializeRaydiumSdk()).rejects.toThrow();
      
      // Should be able to retry initialization
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);
      
      const result = await initializeRaydiumSdk();
      expect(result).toBe(mockSdk);
    });
  });

  describe('getRaydiumSdk', () => {
    test('should throw error if SDK not initialized', () => {
      expect(() => getRaydiumSdk()).toThrow('Raydium SDK not initialized. Call initializeRaydiumSdk() first.');
    });

    test('should return SDK instance if initialized', async () => {
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);

      await initializeRaydiumSdk();
      const result = getRaydiumSdk();

      expect(result).toBe(mockSdk);
    });
  });

  describe('getRaydiumSdkWithInit', () => {
    test('should initialize and return SDK if not already initialized', async () => {
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);

      const result = await getRaydiumSdkWithInit();

      expect(Raydium.load).toHaveBeenCalled();
      expect(result).toBe(mockSdk);
    });

    test('should return existing SDK if already initialized', async () => {
      const mockSdk = global.testUtils.generateMockRaydiumSdk();
      Raydium.load.mockResolvedValue(mockSdk);

      await initializeRaydiumSdk();
      const result = await getRaydiumSdkWithInit();

      expect(Raydium.load).toHaveBeenCalledTimes(1);
      expect(result).toBe(mockSdk);
    });
  });

  describe('getRaydiumPlatformConfig', () => {
    test('should return correct platform configuration', () => {
      const config = getRaydiumPlatformConfig();

      expect(config).toEqual({
        programId: expect.any(Object),
        network: 'devnet',
        slippageBps: 1000,
        priorityFee: {
          unitLimit: 100000,
          unitPrice: 25000
        },
        bundling: {
          maxTransactionsPerBundle: 3,
          maxRetries: 2
        }
      });
    });
  });

  describe('categorizeRaydiumError', () => {
    test('should categorize network errors correctly', () => {
      const error = global.testUtils.generateMockError('NETWORK_ERROR');
      const result = categorizeRaydiumError(error);

      expect(result).toEqual({
        category: 'NETWORK_ERROR',
        severity: 'HIGH',
        retryable: true,
        userMessage: 'Network connection issue. Please try again.'
      });
    });

    test('should categorize insufficient funds errors correctly', () => {
      const error = global.testUtils.generateMockError('INSUFFICIENT_FUNDS');
      const result = categorizeRaydiumError(error);

      expect(result).toEqual({
        category: 'INSUFFICIENT_FUNDS',
        severity: 'MEDIUM',
        retryable: false,
        userMessage: 'Insufficient funds for this transaction.'
      });
    });

    test('should categorize slippage errors correctly', () => {
      const error = global.testUtils.generateMockError('SLIPPAGE_ERROR');
      const result = categorizeRaydiumError(error);

      expect(result).toEqual({
        category: 'SLIPPAGE_ERROR',
        severity: 'MEDIUM',
        retryable: true,
        userMessage: 'Price changed during transaction. Please try again.'
      });
    });

    test('should categorize unknown errors correctly', () => {
      const error = new Error('Unknown error message');
      const result = categorizeRaydiumError(error);

      expect(result).toEqual({
        category: 'UNKNOWN_ERROR',
        severity: 'HIGH',
        retryable: false,
        userMessage: 'An unexpected error occurred. Please try again or contact support.'
      });
    });
  });

  describe('handleRaydiumError', () => {
    test('should handle error with context and wallet index', () => {
      const error = global.testUtils.generateMockError('NETWORK_ERROR');
      const result = handleRaydiumError(error, 'Test operation', 1);

      expect(result).toEqual({
        category: 'NETWORK_ERROR',
        severity: 'HIGH',
        retryable: true,
        userMessage: 'Network connection issue. Please try again.'
      });

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('[Wallet 1] Test operation - NETWORK_ERROR (HIGH)'),
        expect.any(Object)
      );
    });

    test('should handle error without wallet index', () => {
      const error = global.testUtils.generateMockError('SDK_ERROR');
      const result = handleRaydiumError(error, 'SDK initialization');

      expect(result.category).toBe('SDK_ERROR');
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('SDK initialization - SDK_ERROR'),
        expect.any(Object)
      );
    });
  });

  describe('logging functions', () => {
    test('logWithTimestamp should log with timestamp and data', () => {
      logWithTimestamp('Test message', { key: 'value' });

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('RAYDIUM: Test message'),
        { key: 'value' }
      );
    });

    test('logWithTimestamp should log without data', () => {
      logWithTimestamp('Test message');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('RAYDIUM: Test message')
      );
    });

    test('logErrorWithTimestamp should log error with additional details', () => {
      const error = {
        message: 'Test error',
        stack: 'Error stack trace',
        code: 'TEST_ERROR',
        response: { data: 'Response data' },
        logs: ['Log entry 1', 'Log entry 2']
      };

      logErrorWithTimestamp('Test error occurred', error);

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('RAYDIUM ERROR: Test error occurred'),
        error
      );
    });
  });

  describe('handleRaydiumRateLimit', () => {
    test('should handle rate limiting with delay', async () => {
      const delaySpy = jest.spyOn(global.testUtils, 'wait');
      
      await handleRaydiumRateLimit(1);

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Rate limit hit. Retrying in 1000ms (attempt 2/2)')
      );
    });
  });
});
