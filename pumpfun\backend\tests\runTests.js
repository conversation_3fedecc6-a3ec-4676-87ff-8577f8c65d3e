#!/usr/bin/env node

// Test runner script for Raydium SDK V2 integration tests
// This script provides a convenient way to run different test suites

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test suites configuration
const testSuites = {
  unit: {
    name: 'Unit Tests',
    pattern: 'raydium*.test.js',
    description: 'Tests for Raydium configuration and utility functions'
  },
  integration: {
    name: 'Integration Tests',
    pattern: '*Integration.test.js',
    description: 'Tests for integration with existing systems'
  },
  error: {
    name: 'Error Scenario Tests',
    pattern: 'errorScenarios.test.js',
    description: 'Tests for error handling and edge cases'
  },
  performance: {
    name: 'Performance Tests',
    pattern: 'performance.test.js',
    description: 'Performance benchmarks and optimization tests'
  },
  all: {
    name: 'All Tests',
    pattern: '*.test.js',
    description: 'Run all test suites'
  }
};

// Helper functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Run a specific test suite
function runTestSuite(suiteName) {
  const suite = testSuites[suiteName];
  if (!suite) {
    logError(`Unknown test suite: ${suiteName}`);
    return false;
  }

  logHeader(`Running ${suite.name}`);
  logInfo(suite.description);

  try {
    const command = `npx jest --testPathPattern="${suite.pattern}" --verbose`;
    log(`\nExecuting: ${command}`, 'magenta');
    
    execSync(command, { 
      stdio: 'inherit', 
      cwd: path.resolve(__dirname, '..') 
    });
    
    logSuccess(`${suite.name} completed successfully!`);
    return true;
  } catch (error) {
    logError(`${suite.name} failed with exit code: ${error.status}`);
    return false;
  }
}

// Run tests with coverage
function runWithCoverage(suiteName = 'all') {
  const suite = testSuites[suiteName];
  if (!suite) {
    logError(`Unknown test suite: ${suiteName}`);
    return false;
  }

  logHeader(`Running ${suite.name} with Coverage`);
  
  try {
    const command = `npx jest --testPathPattern="${suite.pattern}" --coverage --verbose`;
    log(`\nExecuting: ${command}`, 'magenta');
    
    execSync(command, { 
      stdio: 'inherit', 
      cwd: path.resolve(__dirname, '..') 
    });
    
    logSuccess(`${suite.name} with coverage completed successfully!`);
    return true;
  } catch (error) {
    logError(`${suite.name} with coverage failed with exit code: ${error.status}`);
    return false;
  }
}

// Watch mode for development
function runWatchMode(suiteName = 'all') {
  const suite = testSuites[suiteName];
  if (!suite) {
    logError(`Unknown test suite: ${suiteName}`);
    return false;
  }

  logHeader(`Running ${suite.name} in Watch Mode`);
  logInfo('Press Ctrl+C to exit watch mode');
  
  try {
    const command = `npx jest --testPathPattern="${suite.pattern}" --watch --verbose`;
    log(`\nExecuting: ${command}`, 'magenta');
    
    execSync(command, { 
      stdio: 'inherit', 
      cwd: path.resolve(__dirname, '..') 
    });
    
  } catch (error) {
    if (error.signal === 'SIGINT') {
      logInfo('Watch mode interrupted by user');
    } else {
      logError(`Watch mode failed with exit code: ${error.status}`);
    }
  }
}

// Display help information
function showHelp() {
  logHeader('Raydium SDK V2 Test Runner');
  
  log('\nUsage:', 'bright');
  log('  node runTests.js [command] [suite]');
  
  log('\nCommands:', 'bright');
  log('  run [suite]     Run a specific test suite (default: all)');
  log('  coverage [suite] Run tests with coverage report');
  log('  watch [suite]   Run tests in watch mode');
  log('  help           Show this help message');
  
  log('\nAvailable Test Suites:', 'bright');
  Object.entries(testSuites).forEach(([key, suite]) => {
    log(`  ${key.padEnd(12)} ${suite.name} - ${suite.description}`);
  });
  
  log('\nExamples:', 'bright');
  log('  node runTests.js run unit');
  log('  node runTests.js coverage integration');
  log('  node runTests.js watch performance');
  log('  node runTests.js run all');
  
  log('\nEnvironment Variables:', 'bright');
  log('  NODE_ENV=test   Set test environment (automatically set)');
  log('  VERBOSE=true    Enable verbose output');
}

// Main execution logic
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'run';
  const suite = args[1] || 'all';

  // Set test environment
  process.env.NODE_ENV = 'test';

  switch (command) {
    case 'run':
      runTestSuite(suite);
      break;
    
    case 'coverage':
      runWithCoverage(suite);
      break;
    
    case 'watch':
      runWatchMode(suite);
      break;
    
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    
    default:
      logError(`Unknown command: ${command}`);
      log('\nUse "node runTests.js help" for usage information.');
      process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  runTestSuite,
  runWithCoverage,
  runWatchMode,
  testSuites
};
