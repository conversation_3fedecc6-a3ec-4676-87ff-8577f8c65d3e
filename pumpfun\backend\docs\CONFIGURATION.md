# Configuration Guide for Raydium SDK V2 Integration

## Overview

This document provides detailed information about all configuration options available for the Raydium SDK V2 launchpad integration, including environment variables, runtime settings, and platform-specific configurations.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Runtime Configuration](#runtime-configuration)
3. [Platform-Specific Settings](#platform-specific-settings)
4. [Security Configuration](#security-configuration)
5. [Performance Tuning](#performance-tuning)
6. [Development vs Production](#development-vs-production)
7. [Configuration Validation](#configuration-validation)

## Environment Variables

### Core Application Settings

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `NODE_ENV` | string | `development` | Application environment (development/test/production) |
| `PORT` | number | `3001` | Server port number |
| `API_VERSION` | string | `v1` | API version prefix |
| `LOG_LEVEL` | string | `info` | Logging level (error/warn/info/debug) |

### Database Configuration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `MONGODB_URI` | string | ✅ | MongoDB connection string |
| `MONGODB_OPTIONS` | string | ❌ | Additional MongoDB connection options |
| `DB_POOL_SIZE` | number | `10` | Maximum database connection pool size |
| `DB_TIMEOUT_MS` | number | `30000` | Database operation timeout |

**Example:**
```bash
MONGODB_URI=***************************************************
MONGODB_OPTIONS=retryWrites=true&w=majority&maxPoolSize=10
```

### Solana Network Configuration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `SOLANA_NETWORK` | string | ✅ | Network (devnet/testnet/mainnet-beta) |
| `SOLANA_RPC_URL` | string | ✅ | Primary RPC endpoint URL |
| `SOLANA_RPC_BACKUP_URLS` | string | ❌ | Comma-separated backup RPC URLs |
| `SOLANA_COMMITMENT` | string | `confirmed` | Transaction commitment level |
| `SOLANA_TIMEOUT_MS` | number | `30000` | RPC request timeout |

**Example:**
```bash
SOLANA_NETWORK=mainnet-beta
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_RPC_BACKUP_URLS=https://backup1.com,https://backup2.com
SOLANA_COMMITMENT=confirmed
```

### Raydium SDK Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `RAYDIUM_PROGRAM_ID` | string | `675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8` | Raydium program ID |
| `RAYDIUM_MAX_RETRIES` | number | `3` | Maximum retry attempts for failed operations |
| `RAYDIUM_RETRY_DELAY` | number | `1000` | Delay between retries (milliseconds) |
| `RAYDIUM_DEFAULT_SLIPPAGE_BPS` | number | `1000` | Default slippage tolerance (basis points) |
| `RAYDIUM_SDK_CACHE_TTL` | number | `3600000` | SDK instance cache TTL (milliseconds) |

**Example:**
```bash
RAYDIUM_PROGRAM_ID=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8
RAYDIUM_MAX_RETRIES=3
RAYDIUM_RETRY_DELAY=1000
RAYDIUM_DEFAULT_SLIPPAGE_BPS=1000
```

### Jito Configuration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `JITO_BLOCK_ENGINE_URL` | string | ✅ | Jito block engine endpoint |
| `JITO_TIP_ACCOUNTS` | string | ✅ | JSON array of tip account addresses |
| `JITO_MAX_RETRIES` | number | `3` | Maximum Jito submission retries |
| `JITO_TIMEOUT_MS` | number | `30000` | Jito operation timeout |

**Example:**
```bash
JITO_BLOCK_ENGINE_URL=https://mainnet.block-engine.jito.wtf
JITO_TIP_ACCOUNTS=["Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY"]
JITO_MAX_RETRIES=3
```

### Security Configuration

| Variable | Type | Required | Description |
|----------|------|----------|-------------|
| `JWT_SECRET` | string | ✅ | JWT signing secret (min 32 characters) |
| `ENCRYPTION_KEY` | string | ✅ | Data encryption key (exactly 32 characters) |
| `CORS_ORIGIN` | string | `*` | CORS allowed origins |
| `RATE_LIMIT_WINDOW_MS` | number | `900000` | Rate limiting window (15 minutes) |
| `RATE_LIMIT_MAX_REQUESTS` | number | `100` | Max requests per window |

**Example:**
```bash
JWT_SECRET=your-super-secure-jwt-secret-minimum-32-chars
ENCRYPTION_KEY=your-exactly-32-character-key-here
CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com
```

### Performance Configuration

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `MAX_CONCURRENT_OPERATIONS` | number | `10` | Maximum concurrent wallet operations |
| `TRANSACTION_TIMEOUT_MS` | number | `30000` | Individual transaction timeout |
| `BUNDLE_SIZE_LIMIT` | number | `3` | Maximum transactions per bundle |
| `MEMORY_LIMIT_MB` | number | `2048` | Memory limit for the application |

## Runtime Configuration

### Raydium Configuration Object

The application uses a centralized configuration object for Raydium-specific settings:

```javascript
const RAYDIUM_CONFIG = {
  // Transaction bundling
  MAX_TRANSACTIONS_PER_BUNDLE: parseInt(process.env.BUNDLE_SIZE_LIMIT) || 3,
  
  // Rate limiting and retries
  RATE_LIMIT_RETRY_DELAY: parseInt(process.env.RAYDIUM_RETRY_DELAY) || 1000,
  MAX_RATE_LIMIT_RETRIES: parseInt(process.env.RAYDIUM_MAX_RETRIES) || 2,
  
  // Priority fees
  PRIORITY_FEE_UNIT_LIMIT: parseInt(process.env.PRIORITY_FEE_UNIT_LIMIT) || 100000,
  PRIORITY_FEE_UNIT_PRICE: parseInt(process.env.PRIORITY_FEE_UNIT_PRICE) || 25000,
  
  // Slippage
  DEFAULT_SLIPPAGE_BPS: parseInt(process.env.RAYDIUM_DEFAULT_SLIPPAGE_BPS) || 1000
};
```

### Dynamic Configuration Loading

The application supports dynamic configuration updates through environment variables:

```javascript
// Configuration can be updated at runtime
function updateRaydiumConfig(newConfig) {
  Object.assign(RAYDIUM_CONFIG, newConfig);
  logWithTimestamp('Configuration updated', newConfig);
}
```

## Platform-Specific Settings

### PumpFun Configuration

| Setting | Value | Description |
|---------|-------|-------------|
| Platform Fee | 1% | Fixed platform fee on transactions |
| Slippage | 10% | Fixed slippage protection |
| Migration Threshold | $69k | Market cap for Raydium migration |

### Raydium Configuration

| Setting | Default | Configurable | Description |
|---------|---------|--------------|-------------|
| Platform Fee | Variable | ❌ | Varies by token (1-6%) |
| Slippage | 10% | ✅ | User-configurable slippage |
| Priority Fee | 25000 | ✅ | Configurable priority fee |

## Security Configuration

### Encryption Settings

```bash
# Generate secure encryption key
openssl rand -hex 16  # Generates 32-character hex key

# Example secure configuration
ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcd
JWT_SECRET=super-secure-jwt-secret-with-minimum-32-characters-for-security
```

### CORS Configuration

```bash
# Development (allow all)
CORS_ORIGIN=*

# Production (specific domains)
CORS_ORIGIN=https://app.yourdomain.com,https://admin.yourdomain.com

# Multiple environments
CORS_ORIGIN_DEV=http://localhost:3000,http://localhost:3001
CORS_ORIGIN_PROD=https://yourdomain.com
```

### Rate Limiting

```bash
# Conservative settings
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=50   # 50 requests per window

# Aggressive settings
RATE_LIMIT_WINDOW_MS=300000  # 5 minutes
RATE_LIMIT_MAX_REQUESTS=20   # 20 requests per window
```

## Performance Tuning

### Memory Optimization

```bash
# Node.js memory settings
NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"

# Application memory limits
MEMORY_LIMIT_MB=2048
HEAP_SIZE_LIMIT_MB=1536
```

### Concurrency Settings

```bash
# Conservative (low-resource environments)
MAX_CONCURRENT_OPERATIONS=5
BUNDLE_SIZE_LIMIT=2

# Aggressive (high-resource environments)
MAX_CONCURRENT_OPERATIONS=20
BUNDLE_SIZE_LIMIT=5
```

### Network Optimization

```bash
# Timeout settings
SOLANA_TIMEOUT_MS=15000      # Faster timeout for quick failure
TRANSACTION_TIMEOUT_MS=45000  # Longer timeout for complex operations
JITO_TIMEOUT_MS=20000        # Medium timeout for Jito operations

# Retry settings
RAYDIUM_MAX_RETRIES=5        # More retries for better reliability
RAYDIUM_RETRY_DELAY=2000     # Longer delay between retries
```

## Development vs Production

### Development Configuration

```bash
# .env.development
NODE_ENV=development
LOG_LEVEL=debug
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
CORS_ORIGIN=*
RATE_LIMIT_MAX_REQUESTS=1000
MAX_CONCURRENT_OPERATIONS=20
```

### Production Configuration

```bash
# .env.production
NODE_ENV=production
LOG_LEVEL=info
SOLANA_NETWORK=mainnet-beta
SOLANA_RPC_URL=https://your-premium-rpc.com
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_MAX_REQUESTS=100
MAX_CONCURRENT_OPERATIONS=10
```

### Testing Configuration

```bash
# .env.test
NODE_ENV=test
LOG_LEVEL=error
MONGODB_URI=mongodb://localhost:27017/pumpfun_test
SOLANA_NETWORK=devnet
RAYDIUM_MAX_RETRIES=1
TRANSACTION_TIMEOUT_MS=5000
```

## Configuration Validation

### Environment Validation Script

Create `scripts/validate-config.js`:

```javascript
const requiredVars = [
  'MONGODB_URI',
  'SOLANA_NETWORK',
  'SOLANA_RPC_URL',
  'JWT_SECRET',
  'ENCRYPTION_KEY'
];

const optionalVars = [
  'JITO_BLOCK_ENGINE_URL',
  'RAYDIUM_PROGRAM_ID',
  'CORS_ORIGIN'
];

function validateConfig() {
  const missing = requiredVars.filter(var => !process.env[var]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    process.exit(1);
  }
  
  // Validate encryption key length
  if (process.env.ENCRYPTION_KEY?.length !== 32) {
    console.error('ENCRYPTION_KEY must be exactly 32 characters');
    process.exit(1);
  }
  
  // Validate JWT secret length
  if (process.env.JWT_SECRET?.length < 32) {
    console.error('JWT_SECRET must be at least 32 characters');
    process.exit(1);
  }
  
  console.log('Configuration validation passed');
}

validateConfig();
```

### Runtime Configuration Check

```javascript
// Add to server startup
function checkConfiguration() {
  const config = {
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT,
    database: !!process.env.MONGODB_URI,
    solana: !!process.env.SOLANA_RPC_URL,
    raydium: !!process.env.RAYDIUM_PROGRAM_ID,
    jito: !!process.env.JITO_BLOCK_ENGINE_URL
  };
  
  console.log('Configuration status:', config);
  
  if (!config.database || !config.solana) {
    throw new Error('Critical configuration missing');
  }
}
```

### Configuration Templates

Create configuration templates for different environments:

```bash
# templates/.env.template
NODE_ENV=__ENVIRONMENT__
PORT=__PORT__
MONGODB_URI=__MONGODB_CONNECTION__
SOLANA_NETWORK=__SOLANA_NETWORK__
SOLANA_RPC_URL=__RPC_ENDPOINT__
JWT_SECRET=__JWT_SECRET__
ENCRYPTION_KEY=__ENCRYPTION_KEY__
```

Use with deployment scripts:
```bash
#!/bin/bash
# deploy.sh
envsubst < templates/.env.template > .env
```

For additional configuration options and advanced settings, refer to the API documentation and deployment guide.
