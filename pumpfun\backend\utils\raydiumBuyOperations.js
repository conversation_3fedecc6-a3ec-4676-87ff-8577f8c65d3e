const {
  VersionedTransaction,
  TransactionMessage,
  SystemProgram,
  <PERSON>Key,
  Keypair,
  LAMPORTS_PER_SOL,
  ComputeBudgetProgram
} = require("@solana/web3.js");
const bs58 = require("bs58");
const { parseTokenAccountResp } = require('@raydium-io/raydium-sdk-v2');
const { decrypt } = require("./crypto");
const {
  connection,
  jitoTipAccounts,
  delay
} = require("./config");
const {
  getRaydiumSdkWithInit,
  logWithTimestamp,
  logErrorWithTimestamp,
  handleRaydiumError,
  categorizeRaydiumError,
  RAYDIUM_CONFIG
} = require("./raydiumConfig");
const { submitBundle, getLatestBlockhashWithRetry, submitTransactionToJito } = require("./transactionUtils");

// Priority fee constants for Raydium operations
const PRIORITY_FEE_UNIT_LIMIT = RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_LIMIT;
const PRIORITY_FEE_UNIT_PRICE = RAYDIUM_CONFIG.PRIORITY_FEE_UNIT_PRICE;

/**
 * Buy tokens from Raydium launchpad for multiple wallets
 * Note: This implementation mirrors the PumpFun buyOperations.js structure
 * but uses Raydium SDK V2 launchpad functionality
 * 
 * @param {Array} wallets_ - Array of wallet objects with encrypted private keys
 * @param {Object} amounts - Object mapping wallet public keys to SOL amounts
 * @param {string} token - Token mint address
 * @param {string} jitoTip - Jito tip amount in SOL
 * @param {boolean} isPump - Platform identifier (false for Raydium)
 * @returns {Promise<Object>} - Result object with transaction details
 */
const buyRaydium = async (wallets_, amounts, token, jitoTip, isPump = false) => {
  const startTime = Date.now();
  const walletCount = wallets_.length;
  logWithTimestamp(`Starting Raydium launchpad buy operation for ${walletCount} wallet(s) for token ${token}`);

  try {
    // Initialize Raydium SDK if not already done
    const raydiumSdk = await getRaydiumSdkWithInit();
    logWithTimestamp(`Raydium SDK initialized successfully`);

    // Track which wallets are being processed
    const walletPublicKeys = wallets_.map(w => {
      const wallet = Keypair.fromSecretKey(bs58.decode(decrypt(w.encryptedPrivateKey)));
      return wallet.publicKey.toBase58();
    });
    logWithTimestamp(`Processing wallets:`, walletPublicKeys);

    const buyPromises = wallets_.map(async (walletData, walletIndex) => {
      const walletStartTime = Date.now();
      const wallet = Keypair.fromSecretKey(
        bs58.decode(decrypt(walletData.encryptedPrivateKey))
      );
      const walletPublicKey = wallet.publicKey.toBase58();

      logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Processing Raydium launchpad buy for wallet ${walletPublicKey}`);

      // Get input SOL amount for this wallet
      const amount = amounts[walletPublicKey];
      if (!amount) {
        logErrorWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] No amount specified for wallet ${walletPublicKey}`, { availableAmounts: Object.keys(amounts) });
        throw new Error(`No amount specified for wallet ${walletPublicKey}`);
      }

      logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Amount: ${amount} SOL`);

      // Convert input SOL to lamports
      const solAmountInLamports = BigInt(Number(amount) * LAMPORTS_PER_SOL);
      logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] SOL amount in lamports: ${solAmountInLamports.toString()}`);

      try {
        // Note: The exact Raydium launchpad API methods need to be determined
        // Based on the LaunchLab documentation, there should be methods like:
        // - raydiumSdk.launchpad.buy() or similar
        // - Methods to get launchpad pool information
        // - Bonding curve price calculations
        
        // For now, I'll implement a placeholder structure that follows the
        // Raydium SDK V2 transaction pattern: { execute, transaction, builder, extInfo }
        
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Getting Raydium launchpad pool information`);
        
        // Find pool for the token pair (SOL -> Token)
        const solMint = "So11111111111111111111111111111111111111112"; // SOL mint

        // Get pool information for the token
        const poolData = await raydiumSdk.api.fetchPoolByMints({
          mint1: solMint,
          mint2: token
        });

        if (!poolData || !poolData.data || poolData.data.length === 0) {
          throw new Error(`No Raydium pool found for token ${token}`);
        }

        const pool = poolData.data[0]; // Use first available pool
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Found pool: ${pool.id}`);

        // Get token accounts for the wallet (both TOKEN_PROGRAM_ID and TOKEN_2022_PROGRAM_ID)
        let parsedTokenAccounts = [];

        try {
          // Get SOL account info
          const solAccountResp = await connection.getAccountInfo(wallet.publicKey);

          // Get standard token accounts
          const tokenAccountResp = await connection.getTokenAccountsByOwner(
            wallet.publicKey,
            { programId: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA") }
          );

          // Get Token-2022 accounts
          const token2022Req = await connection.getTokenAccountsByOwner(
            wallet.publicKey,
            { programId: new PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb") }
          );

          // Parse token accounts using Raydium's parser with proper format
          const tokenAccountData = parseTokenAccountResp({
            owner: wallet.publicKey,
            solAccountResp,
            tokenAccountResp: {
              context: tokenAccountResp.context,
              value: [...tokenAccountResp.value, ...token2022Req.value],
            },
          });

          parsedTokenAccounts = tokenAccountData;
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Found ${tokenAccountResp.value.length} standard + ${token2022Req.value.length} Token-2022 accounts`);
        } catch (error) {
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Warning: Could not fetch token accounts: ${error.message}`);
          parsedTokenAccounts = [];
        }

        // Build swap transaction (SOL -> Token)
        const swapResult = await raydiumSdk.liquidity.swap({
          poolInfo: pool,
          amountIn: solAmountInLamports,
          amountOut: 0, // Will be calculated
          fixedSide: 'in',
          inputMint: solMint,
          outputMint: token,
          slippage: RAYDIUM_CONFIG.DEFAULT_SLIPPAGE_BPS / 10000, // Convert basis points to decimal
          owner: wallet.publicKey,
          tokenAccounts: parsedTokenAccounts,
          // Add additional parameters for better account handling
          computeBudgetConfig: {
            units: 100000,
            microLamports: 25000,
          }
        });

        if (!swapResult || !swapResult.transaction) {
          throw new Error('Failed to build swap transaction');
        }

        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Built swap transaction successfully`);
        logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Swap result has ${swapResult.transaction.instructions.length} instructions`);
        
        // Add priority fee instructions
        const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
          microLamports: PRIORITY_FEE_UNIT_PRICE
        });

        const computeUnitLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
          units: PRIORITY_FEE_UNIT_LIMIT
        });

        // Prepare instructions array
        const instructions = [computeUnitLimitInstruction, priorityFeeInstruction];

        // Add Jito tip if specified
        if (Number(jitoTip) > 0) {
          logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Adding Jito tip of ${jitoTip} SOL`);
          const tipAccountNumber = Math.floor(8 * Math.random());
          const tipInstruction = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: jitoTipAccounts[tipAccountNumber],
            lamports: Math.floor(Number(jitoTip) * LAMPORTS_PER_SOL),
          });
          instructions.unshift(tipInstruction);
        }

        // Add the Raydium swap instructions - filter out any undefined or empty instructions
        const validInstructions = swapResult.transaction.instructions.filter(ix => {
          if (!ix || !ix.programId || !ix.data) return false;
          // Filter out instructions with no keys (empty instructions)
          if (!ix.keys || ix.keys.length === 0) {
            logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Filtering out empty instruction`);
            return false;
          }
          return true;
        });
        instructions.push(...validInstructions);

        // Create and execute transaction
        let tries = 0;
        const maxRetries = 3;

        while (tries < maxRetries) {
          try {
            const latestBlockhash = await getLatestBlockhashWithRetry(connection);

            // Validate all instructions have proper accounts
            const validatedInstructions = instructions.map((ix, index) => {
              if (!ix) throw new Error(`Instruction ${index} is null/undefined`);
              if (!ix.keys) ix.keys = [];
              if (!ix.programId) throw new Error(`Instruction ${index} missing programId`);
              if (!ix.data) ix.data = Buffer.alloc(0);

              // Filter out invalid account keys and fix valid ones
              const validKeys = [];
              ix.keys.forEach((key, keyIndex) => {
                if (!key) {
                  logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Instruction ${index}, key ${keyIndex}: null/undefined key - SKIPPING`);
                  return; // Skip this key
                }
                if (!key.pubkey) {
                  logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Instruction ${index}, key ${keyIndex}: missing pubkey - SKIPPING`);
                  return; // Skip this key
                }

                // Ensure pubkey is a PublicKey instance
                try {
                  if (typeof key.pubkey === 'string') {
                    key.pubkey = new PublicKey(key.pubkey);
                  } else if (!key.pubkey.toBase58) {
                    key.pubkey = new PublicKey(key.pubkey);
                  }
                  validKeys.push(key); // Only add valid keys
                } catch (error) {
                  logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Instruction ${index}, key ${keyIndex}: invalid pubkey - SKIPPING - ${error.message}`);
                }
              });

              ix.keys = validKeys;
              logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Instruction ${index}: ${validKeys.length}/${ix.keys.length || 0} valid keys`);
              return ix;
            });

            const buyMessage = new TransactionMessage({
              payerKey: wallet.publicKey,
              recentBlockhash: latestBlockhash.blockhash,
              instructions: validatedInstructions,
            }).compileToV0Message();

            const buyTransaction = new VersionedTransaction(buyMessage);
            buyTransaction.sign([wallet]);

            let signature;

            // For MVP, use regular RPC to avoid Jito rate limiting
            // In production, this would use Jito when jitoTip > 0
            signature = await connection.sendTransaction(buyTransaction, {
              skipPreflight: false,
              maxRetries: 2,
            });

            logWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Transaction submitted successfully: ${signature}`);

            const walletDuration = Date.now() - walletStartTime;
            return {
              walletIndex: walletIndex + 1,
              walletPublicKey,
              signature,
              amount,
              duration: walletDuration,
              success: true
            };

          } catch (error) {
            tries++;
            logErrorWithTimestamp(`[Wallet ${walletIndex + 1}/${walletCount}] Attempt ${tries} failed`, error);

            if (tries >= maxRetries) {
              throw error;
            }

            await delay(500);
          }
        }
        
      } catch (error) {
        const walletDuration = Date.now() - walletStartTime;

        // Use enhanced error handling
        const errorCategory = handleRaydiumError(
          error,
          'Raydium buy operation failed',
          walletIndex + 1
        );

        return {
          walletIndex: walletIndex + 1,
          walletPublicKey,
          error: error.message,
          errorCategory: errorCategory.category,
          errorSeverity: errorCategory.severity,
          retryable: errorCategory.retryable,
          userMessage: errorCategory.userMessage,
          amount,
          duration: walletDuration,
          success: false
        };
      }
    });

    // Wait for all wallet operations to complete
    logWithTimestamp(`Waiting for all ${walletCount} Raydium buy operations to complete...`);
    const results = await Promise.allSettled(buyPromises);
    
    // Process results
    const successfulTxs = [];
    const failedTxs = [];
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        successfulTxs.push(result.value);
      } else {
        const errorInfo = result.status === 'rejected' 
          ? { walletIndex: index + 1, error: result.reason.message }
          : result.value;
        failedTxs.push(errorInfo);
      }
    });
    
    const totalDuration = Date.now() - startTime;
    
    logWithTimestamp(`Raydium buy operation completed in ${totalDuration}ms`);
    logWithTimestamp(`Successful transactions: ${successfulTxs.length}`);
    logWithTimestamp(`Failed transactions: ${failedTxs.length}`);
    
    if (failedTxs.length > 0) {
      logErrorWithTimestamp(`Failed transactions:`, failedTxs);
    }
    
    return {
      success: failedTxs.length === 0,
      totalWallets: walletCount,
      successfulTxs: successfulTxs.length,
      failedTxs: failedTxs.length,
      results: successfulTxs,
      errors: failedTxs,
      duration: totalDuration,
      platform: 'raydium'
    };
    
  } catch (err) {
    const totalDuration = Date.now() - startTime;

    // Use enhanced error handling for the overall operation
    const errorCategory = handleRaydiumError(err, 'Overall Raydium buy operation failed');

    logErrorWithTimestamp(
      `Raydium buy operation failed after ${totalDuration}ms - ${errorCategory.category}`,
      {
        originalError: err.message,
        category: errorCategory.category,
        severity: errorCategory.severity,
        retryable: errorCategory.retryable,
        duration: totalDuration,
        walletCount
      }
    );

    // Re-throw with enhanced error information
    const enhancedError = new Error(errorCategory.userMessage);
    enhancedError.originalError = err;
    enhancedError.category = errorCategory.category;
    enhancedError.severity = errorCategory.severity;
    enhancedError.retryable = errorCategory.retryable;
    enhancedError.duration = totalDuration;

    throw enhancedError;
  }
};

module.exports = {
  buyRaydium
};
